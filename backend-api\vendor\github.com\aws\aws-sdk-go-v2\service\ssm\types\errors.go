// Code generated by smithy-go-codegen DO NOT EDIT.

package types

import (
	"fmt"
	smithy "github.com/aws/smithy-go"
)

// Error returned if an attempt is made to register a patch group with a patch
// baseline that is already registered with a different patch baseline.
type AlreadyExistsException struct {
	Message *string
}

func (e *AlreadyExistsException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AlreadyExistsException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AlreadyExistsException) ErrorCode() string             { return "AlreadyExistsException" }
func (e *AlreadyExistsException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You must disassociate a document from all instances before you can delete it.
type AssociatedInstances struct {
	Message *string
}

func (e *AssociatedInstances) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AssociatedInstances) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AssociatedInstances) ErrorCode() string             { return "AssociatedInstances" }
func (e *AssociatedInstances) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified association already exists.
type AssociationAlreadyExists struct {
	Message *string
}

func (e *AssociationAlreadyExists) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AssociationAlreadyExists) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AssociationAlreadyExists) ErrorCode() string             { return "AssociationAlreadyExists" }
func (e *AssociationAlreadyExists) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified association does not exist.
type AssociationDoesNotExist struct {
	Message *string
}

func (e *AssociationDoesNotExist) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AssociationDoesNotExist) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AssociationDoesNotExist) ErrorCode() string             { return "AssociationDoesNotExist" }
func (e *AssociationDoesNotExist) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified execution ID does not exist. Verify the ID number and try again.
type AssociationExecutionDoesNotExist struct {
	Message *string
}

func (e *AssociationExecutionDoesNotExist) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AssociationExecutionDoesNotExist) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AssociationExecutionDoesNotExist) ErrorCode() string {
	return "AssociationExecutionDoesNotExist"
}
func (e *AssociationExecutionDoesNotExist) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You can have at most 2,000 active associations.
type AssociationLimitExceeded struct {
	Message *string
}

func (e *AssociationLimitExceeded) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AssociationLimitExceeded) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AssociationLimitExceeded) ErrorCode() string             { return "AssociationLimitExceeded" }
func (e *AssociationLimitExceeded) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You have reached the maximum number versions allowed for an association. Each
// association has a limit of 1,000 versions.
type AssociationVersionLimitExceeded struct {
	Message *string
}

func (e *AssociationVersionLimitExceeded) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AssociationVersionLimitExceeded) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AssociationVersionLimitExceeded) ErrorCode() string {
	return "AssociationVersionLimitExceeded"
}
func (e *AssociationVersionLimitExceeded) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Indicates that the Change Manager change template used in the change request was
// rejected or is still in a pending state.
type AutomationDefinitionNotApprovedException struct {
	Message *string
}

func (e *AutomationDefinitionNotApprovedException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AutomationDefinitionNotApprovedException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AutomationDefinitionNotApprovedException) ErrorCode() string {
	return "AutomationDefinitionNotApprovedException"
}
func (e *AutomationDefinitionNotApprovedException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// An Automation document with the specified name could not be found.
type AutomationDefinitionNotFoundException struct {
	Message *string
}

func (e *AutomationDefinitionNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AutomationDefinitionNotFoundException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AutomationDefinitionNotFoundException) ErrorCode() string {
	return "AutomationDefinitionNotFoundException"
}
func (e *AutomationDefinitionNotFoundException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// An Automation document with the specified name and version could not be found.
type AutomationDefinitionVersionNotFoundException struct {
	Message *string
}

func (e *AutomationDefinitionVersionNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AutomationDefinitionVersionNotFoundException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AutomationDefinitionVersionNotFoundException) ErrorCode() string {
	return "AutomationDefinitionVersionNotFoundException"
}
func (e *AutomationDefinitionVersionNotFoundException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The number of simultaneously running Automation executions exceeded the
// allowable limit.
type AutomationExecutionLimitExceededException struct {
	Message *string
}

func (e *AutomationExecutionLimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AutomationExecutionLimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AutomationExecutionLimitExceededException) ErrorCode() string {
	return "AutomationExecutionLimitExceededException"
}
func (e *AutomationExecutionLimitExceededException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// There is no automation execution information for the requested automation
// execution ID.
type AutomationExecutionNotFoundException struct {
	Message *string
}

func (e *AutomationExecutionNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AutomationExecutionNotFoundException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AutomationExecutionNotFoundException) ErrorCode() string {
	return "AutomationExecutionNotFoundException"
}
func (e *AutomationExecutionNotFoundException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The specified step name and execution ID don't exist. Verify the information and
// try again.
type AutomationStepNotFoundException struct {
	Message *string
}

func (e *AutomationStepNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *AutomationStepNotFoundException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *AutomationStepNotFoundException) ErrorCode() string {
	return "AutomationStepNotFoundException"
}
func (e *AutomationStepNotFoundException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You specified too many custom compliance types. You can specify a maximum of 10
// different types.
type ComplianceTypeCountLimitExceededException struct {
	Message *string
}

func (e *ComplianceTypeCountLimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ComplianceTypeCountLimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ComplianceTypeCountLimitExceededException) ErrorCode() string {
	return "ComplianceTypeCountLimitExceededException"
}
func (e *ComplianceTypeCountLimitExceededException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// You have exceeded the limit for custom schemas. Delete one or more custom
// schemas and try again.
type CustomSchemaCountLimitExceededException struct {
	Message *string
}

func (e *CustomSchemaCountLimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *CustomSchemaCountLimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *CustomSchemaCountLimitExceededException) ErrorCode() string {
	return "CustomSchemaCountLimitExceededException"
}
func (e *CustomSchemaCountLimitExceededException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The specified document already exists.
type DocumentAlreadyExists struct {
	Message *string
}

func (e *DocumentAlreadyExists) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *DocumentAlreadyExists) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *DocumentAlreadyExists) ErrorCode() string             { return "DocumentAlreadyExists" }
func (e *DocumentAlreadyExists) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You can have at most 500 active Systems Manager documents.
type DocumentLimitExceeded struct {
	Message *string
}

func (e *DocumentLimitExceeded) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *DocumentLimitExceeded) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *DocumentLimitExceeded) ErrorCode() string             { return "DocumentLimitExceeded" }
func (e *DocumentLimitExceeded) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The document cannot be shared with more AWS user accounts. You can share a
// document with a maximum of 20 accounts. You can publicly share up to five
// documents. If you need to increase this limit, contact AWS Support.
type DocumentPermissionLimit struct {
	Message *string
}

func (e *DocumentPermissionLimit) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *DocumentPermissionLimit) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *DocumentPermissionLimit) ErrorCode() string             { return "DocumentPermissionLimit" }
func (e *DocumentPermissionLimit) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The document has too many versions. Delete one or more document versions and try
// again.
type DocumentVersionLimitExceeded struct {
	Message *string
}

func (e *DocumentVersionLimitExceeded) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *DocumentVersionLimitExceeded) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *DocumentVersionLimitExceeded) ErrorCode() string             { return "DocumentVersionLimitExceeded" }
func (e *DocumentVersionLimitExceeded) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Error returned when the ID specified for a resource, such as a maintenance
// window or Patch baseline, doesn't exist. For information about resource quotas
// in Systems Manager, see Systems Manager service quotas
// (http://docs.aws.amazon.com/general/latest/gr/ssm.html#limits_ssm) in the AWS
// General Reference.
type DoesNotExistException struct {
	Message *string
}

func (e *DoesNotExistException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *DoesNotExistException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *DoesNotExistException) ErrorCode() string             { return "DoesNotExistException" }
func (e *DoesNotExistException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The content of the association document matches another document. Change the
// content of the document and try again.
type DuplicateDocumentContent struct {
	Message *string
}

func (e *DuplicateDocumentContent) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *DuplicateDocumentContent) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *DuplicateDocumentContent) ErrorCode() string             { return "DuplicateDocumentContent" }
func (e *DuplicateDocumentContent) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The version name has already been used in this document. Specify a different
// version name, and then try again.
type DuplicateDocumentVersionName struct {
	Message *string
}

func (e *DuplicateDocumentVersionName) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *DuplicateDocumentVersionName) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *DuplicateDocumentVersionName) ErrorCode() string             { return "DuplicateDocumentVersionName" }
func (e *DuplicateDocumentVersionName) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You cannot specify an instance ID in more than one association.
type DuplicateInstanceId struct {
	Message *string
}

func (e *DuplicateInstanceId) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *DuplicateInstanceId) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *DuplicateInstanceId) ErrorCode() string             { return "DuplicateInstanceId" }
func (e *DuplicateInstanceId) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You attempted to register a LAMBDA or STEP_FUNCTIONS task in a region where the
// corresponding service is not available.
type FeatureNotAvailableException struct {
	Message *string
}

func (e *FeatureNotAvailableException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *FeatureNotAvailableException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *FeatureNotAvailableException) ErrorCode() string             { return "FeatureNotAvailableException" }
func (e *FeatureNotAvailableException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// A hierarchy can have a maximum of 15 levels. For more information, see
// Requirements and constraints for parameter names
// (https://docs.aws.amazon.com/systems-manager/latest/userguide/sysman-parameter-name-constraints.html)
// in the AWS Systems Manager User Guide.
type HierarchyLevelLimitExceededException struct {
	Message *string
}

func (e *HierarchyLevelLimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *HierarchyLevelLimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *HierarchyLevelLimitExceededException) ErrorCode() string {
	return "HierarchyLevelLimitExceededException"
}
func (e *HierarchyLevelLimitExceededException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// Parameter Store does not support changing a parameter type in a hierarchy. For
// example, you can't change a parameter from a String type to a SecureString type.
// You must create a new, unique parameter.
type HierarchyTypeMismatchException struct {
	Message *string
}

func (e *HierarchyTypeMismatchException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *HierarchyTypeMismatchException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *HierarchyTypeMismatchException) ErrorCode() string             { return "HierarchyTypeMismatchException" }
func (e *HierarchyTypeMismatchException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Error returned when an idempotent operation is retried and the parameters don't
// match the original call to the API with the same idempotency token.
type IdempotentParameterMismatch struct {
	Message *string
}

func (e *IdempotentParameterMismatch) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *IdempotentParameterMismatch) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *IdempotentParameterMismatch) ErrorCode() string             { return "IdempotentParameterMismatch" }
func (e *IdempotentParameterMismatch) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// There is a conflict in the policies specified for this parameter. You can't, for
// example, specify two Expiration policies for a parameter. Review your policies,
// and try again.
type IncompatiblePolicyException struct {
	Message *string
}

func (e *IncompatiblePolicyException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *IncompatiblePolicyException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *IncompatiblePolicyException) ErrorCode() string             { return "IncompatiblePolicyException" }
func (e *IncompatiblePolicyException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// An error occurred on the server side.
type InternalServerError struct {
	Message *string
}

func (e *InternalServerError) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InternalServerError) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InternalServerError) ErrorCode() string             { return "InternalServerError" }
func (e *InternalServerError) ErrorFault() smithy.ErrorFault { return smithy.FaultServer }

// The activation is not valid. The activation might have been deleted, or the
// ActivationId and the ActivationCode do not match.
type InvalidActivation struct {
	Message *string
}

func (e *InvalidActivation) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidActivation) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidActivation) ErrorCode() string             { return "InvalidActivation" }
func (e *InvalidActivation) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The activation ID is not valid. Verify the you entered the correct ActivationId
// or ActivationCode and try again.
type InvalidActivationId struct {
	Message *string
}

func (e *InvalidActivationId) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidActivationId) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidActivationId) ErrorCode() string             { return "InvalidActivationId" }
func (e *InvalidActivationId) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified aggregator is not valid for inventory groups. Verify that the
// aggregator uses a valid inventory type such as AWS:Application or
// AWS:InstanceInformation.
type InvalidAggregatorException struct {
	Message *string
}

func (e *InvalidAggregatorException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidAggregatorException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidAggregatorException) ErrorCode() string             { return "InvalidAggregatorException" }
func (e *InvalidAggregatorException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request does not meet the regular expression requirement.
type InvalidAllowedPatternException struct {
	Message *string
}

func (e *InvalidAllowedPatternException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidAllowedPatternException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidAllowedPatternException) ErrorCode() string             { return "InvalidAllowedPatternException" }
func (e *InvalidAllowedPatternException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The association is not valid or does not exist.
type InvalidAssociation struct {
	Message *string
}

func (e *InvalidAssociation) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidAssociation) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidAssociation) ErrorCode() string             { return "InvalidAssociation" }
func (e *InvalidAssociation) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The version you specified is not valid. Use ListAssociationVersions to view all
// versions of an association according to the association ID. Or, use the $LATEST
// parameter to view the latest version of the association.
type InvalidAssociationVersion struct {
	Message *string
}

func (e *InvalidAssociationVersion) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidAssociationVersion) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidAssociationVersion) ErrorCode() string             { return "InvalidAssociationVersion" }
func (e *InvalidAssociationVersion) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The supplied parameters for invoking the specified Automation document are
// incorrect. For example, they may not match the set of parameters permitted for
// the specified Automation document.
type InvalidAutomationExecutionParametersException struct {
	Message *string
}

func (e *InvalidAutomationExecutionParametersException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidAutomationExecutionParametersException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidAutomationExecutionParametersException) ErrorCode() string {
	return "InvalidAutomationExecutionParametersException"
}
func (e *InvalidAutomationExecutionParametersException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The signal is not valid for the current Automation execution.
type InvalidAutomationSignalException struct {
	Message *string
}

func (e *InvalidAutomationSignalException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidAutomationSignalException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidAutomationSignalException) ErrorCode() string {
	return "InvalidAutomationSignalException"
}
func (e *InvalidAutomationSignalException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified update status operation is not valid.
type InvalidAutomationStatusUpdateException struct {
	Message *string
}

func (e *InvalidAutomationStatusUpdateException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidAutomationStatusUpdateException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidAutomationStatusUpdateException) ErrorCode() string {
	return "InvalidAutomationStatusUpdateException"
}
func (e *InvalidAutomationStatusUpdateException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

type InvalidCommandId struct {
	Message *string
}

func (e *InvalidCommandId) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidCommandId) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidCommandId) ErrorCode() string             { return "InvalidCommandId" }
func (e *InvalidCommandId) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// One or more of the parameters specified for the delete operation is not valid.
// Verify all parameters and try again.
type InvalidDeleteInventoryParametersException struct {
	Message *string
}

func (e *InvalidDeleteInventoryParametersException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidDeleteInventoryParametersException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidDeleteInventoryParametersException) ErrorCode() string {
	return "InvalidDeleteInventoryParametersException"
}
func (e *InvalidDeleteInventoryParametersException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The ID specified for the delete operation does not exist or is not valid. Verify
// the ID and try again.
type InvalidDeletionIdException struct {
	Message *string
}

func (e *InvalidDeletionIdException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidDeletionIdException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidDeletionIdException) ErrorCode() string             { return "InvalidDeletionIdException" }
func (e *InvalidDeletionIdException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified document does not exist.
type InvalidDocument struct {
	Message *string
}

func (e *InvalidDocument) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidDocument) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidDocument) ErrorCode() string             { return "InvalidDocument" }
func (e *InvalidDocument) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The content for the document is not valid.
type InvalidDocumentContent struct {
	Message *string
}

func (e *InvalidDocumentContent) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidDocumentContent) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidDocumentContent) ErrorCode() string             { return "InvalidDocumentContent" }
func (e *InvalidDocumentContent) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You attempted to delete a document while it is still shared. You must stop
// sharing the document before you can delete it.
type InvalidDocumentOperation struct {
	Message *string
}

func (e *InvalidDocumentOperation) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidDocumentOperation) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidDocumentOperation) ErrorCode() string             { return "InvalidDocumentOperation" }
func (e *InvalidDocumentOperation) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The version of the document schema is not supported.
type InvalidDocumentSchemaVersion struct {
	Message *string
}

func (e *InvalidDocumentSchemaVersion) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidDocumentSchemaVersion) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidDocumentSchemaVersion) ErrorCode() string             { return "InvalidDocumentSchemaVersion" }
func (e *InvalidDocumentSchemaVersion) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The document type is not valid. Valid document types are described in the
// DocumentType property.
type InvalidDocumentType struct {
	Message *string
}

func (e *InvalidDocumentType) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidDocumentType) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidDocumentType) ErrorCode() string             { return "InvalidDocumentType" }
func (e *InvalidDocumentType) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The document version is not valid or does not exist.
type InvalidDocumentVersion struct {
	Message *string
}

func (e *InvalidDocumentVersion) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidDocumentVersion) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidDocumentVersion) ErrorCode() string             { return "InvalidDocumentVersion" }
func (e *InvalidDocumentVersion) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The filter name is not valid. Verify the you entered the correct name and try
// again.
type InvalidFilter struct {
	Message *string
}

func (e *InvalidFilter) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidFilter) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidFilter) ErrorCode() string             { return "InvalidFilter" }
func (e *InvalidFilter) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified key is not valid.
type InvalidFilterKey struct {
	Message *string
}

func (e *InvalidFilterKey) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidFilterKey) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidFilterKey) ErrorCode() string             { return "InvalidFilterKey" }
func (e *InvalidFilterKey) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified filter option is not valid. Valid options are Equals and
// BeginsWith. For Path filter, valid options are Recursive and OneLevel.
type InvalidFilterOption struct {
	Message *string
}

func (e *InvalidFilterOption) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidFilterOption) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidFilterOption) ErrorCode() string             { return "InvalidFilterOption" }
func (e *InvalidFilterOption) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The filter value is not valid. Verify the value and try again.
type InvalidFilterValue struct {
	Message *string
}

func (e *InvalidFilterValue) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidFilterValue) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidFilterValue) ErrorCode() string             { return "InvalidFilterValue" }
func (e *InvalidFilterValue) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The following problems can cause this exception: You do not have permission to
// access the instance. SSM Agent is not running. Verify that SSM Agent is running.
// SSM Agent is not registered with the SSM endpoint. Try reinstalling SSM Agent.
// The instance is not in valid state. Valid states are: Running, Pending, Stopped,
// Stopping. Invalid states are: Shutting-down and Terminated.
type InvalidInstanceId struct {
	Message *string
}

func (e *InvalidInstanceId) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidInstanceId) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidInstanceId) ErrorCode() string             { return "InvalidInstanceId" }
func (e *InvalidInstanceId) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified filter value is not valid.
type InvalidInstanceInformationFilterValue struct {
	Message *string
}

func (e *InvalidInstanceInformationFilterValue) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidInstanceInformationFilterValue) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidInstanceInformationFilterValue) ErrorCode() string {
	return "InvalidInstanceInformationFilterValue"
}
func (e *InvalidInstanceInformationFilterValue) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The specified inventory group is not valid.
type InvalidInventoryGroupException struct {
	Message *string
}

func (e *InvalidInventoryGroupException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidInventoryGroupException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidInventoryGroupException) ErrorCode() string             { return "InvalidInventoryGroupException" }
func (e *InvalidInventoryGroupException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You specified invalid keys or values in the Context attribute for InventoryItem.
// Verify the keys and values, and try again.
type InvalidInventoryItemContextException struct {
	Message *string
}

func (e *InvalidInventoryItemContextException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidInventoryItemContextException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidInventoryItemContextException) ErrorCode() string {
	return "InvalidInventoryItemContextException"
}
func (e *InvalidInventoryItemContextException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The request is not valid.
type InvalidInventoryRequestException struct {
	Message *string
}

func (e *InvalidInventoryRequestException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidInventoryRequestException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidInventoryRequestException) ErrorCode() string {
	return "InvalidInventoryRequestException"
}
func (e *InvalidInventoryRequestException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// One or more content items is not valid.
type InvalidItemContentException struct {
	Message *string

	TypeName *string
}

func (e *InvalidItemContentException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidItemContentException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidItemContentException) ErrorCode() string             { return "InvalidItemContentException" }
func (e *InvalidItemContentException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The query key ID is not valid.
type InvalidKeyId struct {
	Message *string
}

func (e *InvalidKeyId) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidKeyId) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidKeyId) ErrorCode() string             { return "InvalidKeyId" }
func (e *InvalidKeyId) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified token is not valid.
type InvalidNextToken struct {
	Message *string
}

func (e *InvalidNextToken) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidNextToken) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidNextToken) ErrorCode() string             { return "InvalidNextToken" }
func (e *InvalidNextToken) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// One or more configuration items is not valid. Verify that a valid Amazon
// Resource Name (ARN) was provided for an Amazon SNS topic.
type InvalidNotificationConfig struct {
	Message *string
}

func (e *InvalidNotificationConfig) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidNotificationConfig) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidNotificationConfig) ErrorCode() string             { return "InvalidNotificationConfig" }
func (e *InvalidNotificationConfig) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The delete inventory option specified is not valid. Verify the option and try
// again.
type InvalidOptionException struct {
	Message *string
}

func (e *InvalidOptionException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidOptionException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidOptionException) ErrorCode() string             { return "InvalidOptionException" }
func (e *InvalidOptionException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The S3 bucket does not exist.
type InvalidOutputFolder struct {
	Message *string
}

func (e *InvalidOutputFolder) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidOutputFolder) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidOutputFolder) ErrorCode() string             { return "InvalidOutputFolder" }
func (e *InvalidOutputFolder) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The output location is not valid or does not exist.
type InvalidOutputLocation struct {
	Message *string
}

func (e *InvalidOutputLocation) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidOutputLocation) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidOutputLocation) ErrorCode() string             { return "InvalidOutputLocation" }
func (e *InvalidOutputLocation) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You must specify values for all required parameters in the Systems Manager
// document. You can only supply values to parameters defined in the Systems
// Manager document.
type InvalidParameters struct {
	Message *string
}

func (e *InvalidParameters) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidParameters) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidParameters) ErrorCode() string             { return "InvalidParameters" }
func (e *InvalidParameters) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The permission type is not supported. Share is the only supported permission
// type.
type InvalidPermissionType struct {
	Message *string
}

func (e *InvalidPermissionType) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidPermissionType) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidPermissionType) ErrorCode() string             { return "InvalidPermissionType" }
func (e *InvalidPermissionType) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The plugin name is not valid.
type InvalidPluginName struct {
	Message *string
}

func (e *InvalidPluginName) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidPluginName) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidPluginName) ErrorCode() string             { return "InvalidPluginName" }
func (e *InvalidPluginName) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// A policy attribute or its value is invalid.
type InvalidPolicyAttributeException struct {
	Message *string
}

func (e *InvalidPolicyAttributeException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidPolicyAttributeException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidPolicyAttributeException) ErrorCode() string {
	return "InvalidPolicyAttributeException"
}
func (e *InvalidPolicyAttributeException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The policy type is not supported. Parameter Store supports the following policy
// types: Expiration, ExpirationNotification, and NoChangeNotification.
type InvalidPolicyTypeException struct {
	Message *string
}

func (e *InvalidPolicyTypeException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidPolicyTypeException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidPolicyTypeException) ErrorCode() string             { return "InvalidPolicyTypeException" }
func (e *InvalidPolicyTypeException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The resource ID is not valid. Verify that you entered the correct ID and try
// again.
type InvalidResourceId struct {
	Message *string
}

func (e *InvalidResourceId) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidResourceId) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidResourceId) ErrorCode() string             { return "InvalidResourceId" }
func (e *InvalidResourceId) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The resource type is not valid. For example, if you are attempting to tag an
// instance, the instance must be a registered, managed instance.
type InvalidResourceType struct {
	Message *string
}

func (e *InvalidResourceType) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidResourceType) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidResourceType) ErrorCode() string             { return "InvalidResourceType" }
func (e *InvalidResourceType) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified inventory item result attribute is not valid.
type InvalidResultAttributeException struct {
	Message *string
}

func (e *InvalidResultAttributeException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidResultAttributeException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidResultAttributeException) ErrorCode() string {
	return "InvalidResultAttributeException"
}
func (e *InvalidResultAttributeException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The role name can't contain invalid characters. Also verify that you specified
// an IAM role for notifications that includes the required trust policy. For
// information about configuring the IAM role for Run Command notifications, see
// Configuring Amazon SNS Notifications for Run Command
// (https://docs.aws.amazon.com/systems-manager/latest/userguide/rc-sns-notifications.html)
// in the AWS Systems Manager User Guide.
type InvalidRole struct {
	Message *string
}

func (e *InvalidRole) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidRole) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidRole) ErrorCode() string             { return "InvalidRole" }
func (e *InvalidRole) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The schedule is invalid. Verify your cron or rate expression and try again.
type InvalidSchedule struct {
	Message *string
}

func (e *InvalidSchedule) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidSchedule) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidSchedule) ErrorCode() string             { return "InvalidSchedule" }
func (e *InvalidSchedule) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The target is not valid or does not exist. It might not be configured for
// Systems Manager or you might not have permission to perform the operation.
type InvalidTarget struct {
	Message *string
}

func (e *InvalidTarget) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidTarget) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidTarget) ErrorCode() string             { return "InvalidTarget" }
func (e *InvalidTarget) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The parameter type name is not valid.
type InvalidTypeNameException struct {
	Message *string
}

func (e *InvalidTypeNameException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidTypeNameException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidTypeNameException) ErrorCode() string             { return "InvalidTypeNameException" }
func (e *InvalidTypeNameException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The update is not valid.
type InvalidUpdate struct {
	Message *string
}

func (e *InvalidUpdate) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvalidUpdate) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvalidUpdate) ErrorCode() string             { return "InvalidUpdate" }
func (e *InvalidUpdate) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The command ID and instance ID you specified did not match any invocations.
// Verify the command ID and the instance ID and try again.
type InvocationDoesNotExist struct {
	Message *string
}

func (e *InvocationDoesNotExist) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *InvocationDoesNotExist) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *InvocationDoesNotExist) ErrorCode() string             { return "InvocationDoesNotExist" }
func (e *InvocationDoesNotExist) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The inventory item has invalid content.
type ItemContentMismatchException struct {
	Message *string

	TypeName *string
}

func (e *ItemContentMismatchException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ItemContentMismatchException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ItemContentMismatchException) ErrorCode() string             { return "ItemContentMismatchException" }
func (e *ItemContentMismatchException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The inventory item size has exceeded the size limit.
type ItemSizeLimitExceededException struct {
	Message *string

	TypeName *string
}

func (e *ItemSizeLimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ItemSizeLimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ItemSizeLimitExceededException) ErrorCode() string             { return "ItemSizeLimitExceededException" }
func (e *ItemSizeLimitExceededException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The size limit of a document is 64 KB.
type MaxDocumentSizeExceeded struct {
	Message *string
}

func (e *MaxDocumentSizeExceeded) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *MaxDocumentSizeExceeded) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *MaxDocumentSizeExceeded) ErrorCode() string             { return "MaxDocumentSizeExceeded" }
func (e *MaxDocumentSizeExceeded) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The OpsItem already exists.
type OpsItemAlreadyExistsException struct {
	Message *string

	OpsItemId *string
}

func (e *OpsItemAlreadyExistsException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *OpsItemAlreadyExistsException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *OpsItemAlreadyExistsException) ErrorCode() string             { return "OpsItemAlreadyExistsException" }
func (e *OpsItemAlreadyExistsException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// A specified parameter argument isn't valid. Verify the available arguments and
// try again.
type OpsItemInvalidParameterException struct {
	Message *string

	ParameterNames []string
}

func (e *OpsItemInvalidParameterException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *OpsItemInvalidParameterException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *OpsItemInvalidParameterException) ErrorCode() string {
	return "OpsItemInvalidParameterException"
}
func (e *OpsItemInvalidParameterException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The request caused OpsItems to exceed one or more quotas. For information about
// OpsItem quotas, see What are the resource limits for OpsCenter?
// (https://docs.aws.amazon.com/systems-manager/latest/userguide/OpsCenter-learn-more.html#OpsCenter-learn-more-limits).
type OpsItemLimitExceededException struct {
	Message *string

	ResourceTypes []string
	Limit         int32
	LimitType     *string
}

func (e *OpsItemLimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *OpsItemLimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *OpsItemLimitExceededException) ErrorCode() string             { return "OpsItemLimitExceededException" }
func (e *OpsItemLimitExceededException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified OpsItem ID doesn't exist. Verify the ID and try again.
type OpsItemNotFoundException struct {
	Message *string
}

func (e *OpsItemNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *OpsItemNotFoundException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *OpsItemNotFoundException) ErrorCode() string             { return "OpsItemNotFoundException" }
func (e *OpsItemNotFoundException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// An OpsMetadata object already exists for the selected resource.
type OpsMetadataAlreadyExistsException struct {
	Message *string
}

func (e *OpsMetadataAlreadyExistsException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *OpsMetadataAlreadyExistsException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *OpsMetadataAlreadyExistsException) ErrorCode() string {
	return "OpsMetadataAlreadyExistsException"
}
func (e *OpsMetadataAlreadyExistsException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// One of the arguments passed is invalid.
type OpsMetadataInvalidArgumentException struct {
	Message *string
}

func (e *OpsMetadataInvalidArgumentException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *OpsMetadataInvalidArgumentException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *OpsMetadataInvalidArgumentException) ErrorCode() string {
	return "OpsMetadataInvalidArgumentException"
}
func (e *OpsMetadataInvalidArgumentException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The OpsMetadata object exceeds the maximum number of OpsMetadata keys that you
// can assign to an application in Application Manager.
type OpsMetadataKeyLimitExceededException struct {
	Message *string
}

func (e *OpsMetadataKeyLimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *OpsMetadataKeyLimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *OpsMetadataKeyLimitExceededException) ErrorCode() string {
	return "OpsMetadataKeyLimitExceededException"
}
func (e *OpsMetadataKeyLimitExceededException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// Your account reached the maximum number of OpsMetadata objects allowed by
// Application Manager. The maximum is 200 OpsMetadata objects. Delete one or more
// OpsMetadata object and try again.
type OpsMetadataLimitExceededException struct {
	Message *string
}

func (e *OpsMetadataLimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *OpsMetadataLimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *OpsMetadataLimitExceededException) ErrorCode() string {
	return "OpsMetadataLimitExceededException"
}
func (e *OpsMetadataLimitExceededException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The OpsMetadata object does not exist.
type OpsMetadataNotFoundException struct {
	Message *string
}

func (e *OpsMetadataNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *OpsMetadataNotFoundException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *OpsMetadataNotFoundException) ErrorCode() string             { return "OpsMetadataNotFoundException" }
func (e *OpsMetadataNotFoundException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The system is processing too many concurrent updates. Wait a few moments and try
// again.
type OpsMetadataTooManyUpdatesException struct {
	Message *string
}

func (e *OpsMetadataTooManyUpdatesException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *OpsMetadataTooManyUpdatesException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *OpsMetadataTooManyUpdatesException) ErrorCode() string {
	return "OpsMetadataTooManyUpdatesException"
}
func (e *OpsMetadataTooManyUpdatesException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The parameter already exists. You can't create duplicate parameters.
type ParameterAlreadyExists struct {
	Message *string
}

func (e *ParameterAlreadyExists) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ParameterAlreadyExists) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ParameterAlreadyExists) ErrorCode() string             { return "ParameterAlreadyExists" }
func (e *ParameterAlreadyExists) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You have exceeded the number of parameters for this AWS account. Delete one or
// more parameters and try again.
type ParameterLimitExceeded struct {
	Message *string
}

func (e *ParameterLimitExceeded) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ParameterLimitExceeded) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ParameterLimitExceeded) ErrorCode() string             { return "ParameterLimitExceeded" }
func (e *ParameterLimitExceeded) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Parameter Store retains the 100 most recently created versions of a parameter.
// After this number of versions has been created, Parameter Store deletes the
// oldest version when a new one is created. However, if the oldest version has a
// label attached to it, Parameter Store will not delete the version and instead
// presents this error message: An error occurred
// (ParameterMaxVersionLimitExceeded) when calling the PutParameter operation: You
// attempted to create a new version of parameter-name by calling the PutParameter
// API with the overwrite flag. Version version-number, the oldest version, can't
// be deleted because it has a label associated with it. Move the label to another
// version of the parameter, and try again. This safeguard is to prevent parameter
// versions with mission critical labels assigned to them from being deleted. To
// continue creating new parameters, first move the label from the oldest version
// of the parameter to a newer one for use in your operations. For information
// about moving parameter labels, see Move a parameter label (console)
// (http://docs.aws.amazon.com/systems-manager/latest/userguide/sysman-paramstore-labels.html#sysman-paramstore-labels-console-move)
// or Move a parameter label (CLI)
// (http://docs.aws.amazon.com/systems-manager/latest/userguide/sysman-paramstore-labels.html#sysman-paramstore-labels-cli-move)
// in the AWS Systems Manager User Guide.
type ParameterMaxVersionLimitExceeded struct {
	Message *string
}

func (e *ParameterMaxVersionLimitExceeded) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ParameterMaxVersionLimitExceeded) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ParameterMaxVersionLimitExceeded) ErrorCode() string {
	return "ParameterMaxVersionLimitExceeded"
}
func (e *ParameterMaxVersionLimitExceeded) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The parameter could not be found. Verify the name and try again.
type ParameterNotFound struct {
	Message *string
}

func (e *ParameterNotFound) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ParameterNotFound) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ParameterNotFound) ErrorCode() string             { return "ParameterNotFound" }
func (e *ParameterNotFound) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The parameter name is not valid.
type ParameterPatternMismatchException struct {
	Message *string
}

func (e *ParameterPatternMismatchException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ParameterPatternMismatchException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ParameterPatternMismatchException) ErrorCode() string {
	return "ParameterPatternMismatchException"
}
func (e *ParameterPatternMismatchException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// A parameter version can have a maximum of ten labels.
type ParameterVersionLabelLimitExceeded struct {
	Message *string
}

func (e *ParameterVersionLabelLimitExceeded) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ParameterVersionLabelLimitExceeded) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ParameterVersionLabelLimitExceeded) ErrorCode() string {
	return "ParameterVersionLabelLimitExceeded"
}
func (e *ParameterVersionLabelLimitExceeded) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The specified parameter version was not found. Verify the parameter name and
// version, and try again.
type ParameterVersionNotFound struct {
	Message *string
}

func (e *ParameterVersionNotFound) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ParameterVersionNotFound) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ParameterVersionNotFound) ErrorCode() string             { return "ParameterVersionNotFound" }
func (e *ParameterVersionNotFound) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You specified more than the maximum number of allowed policies for the
// parameter. The maximum is 10.
type PoliciesLimitExceededException struct {
	Message *string
}

func (e *PoliciesLimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *PoliciesLimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *PoliciesLimitExceededException) ErrorCode() string             { return "PoliciesLimitExceededException" }
func (e *PoliciesLimitExceededException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// A sync configuration with the same name already exists.
type ResourceDataSyncAlreadyExistsException struct {
	Message *string

	SyncName *string
}

func (e *ResourceDataSyncAlreadyExistsException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ResourceDataSyncAlreadyExistsException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ResourceDataSyncAlreadyExistsException) ErrorCode() string {
	return "ResourceDataSyncAlreadyExistsException"
}
func (e *ResourceDataSyncAlreadyExistsException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// Another UpdateResourceDataSync request is being processed. Wait a few minutes
// and try again.
type ResourceDataSyncConflictException struct {
	Message *string
}

func (e *ResourceDataSyncConflictException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ResourceDataSyncConflictException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ResourceDataSyncConflictException) ErrorCode() string {
	return "ResourceDataSyncConflictException"
}
func (e *ResourceDataSyncConflictException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// You have exceeded the allowed maximum sync configurations.
type ResourceDataSyncCountExceededException struct {
	Message *string
}

func (e *ResourceDataSyncCountExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ResourceDataSyncCountExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ResourceDataSyncCountExceededException) ErrorCode() string {
	return "ResourceDataSyncCountExceededException"
}
func (e *ResourceDataSyncCountExceededException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The specified sync configuration is invalid.
type ResourceDataSyncInvalidConfigurationException struct {
	Message *string
}

func (e *ResourceDataSyncInvalidConfigurationException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ResourceDataSyncInvalidConfigurationException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ResourceDataSyncInvalidConfigurationException) ErrorCode() string {
	return "ResourceDataSyncInvalidConfigurationException"
}
func (e *ResourceDataSyncInvalidConfigurationException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The specified sync name was not found.
type ResourceDataSyncNotFoundException struct {
	Message *string

	SyncName *string
	SyncType *string
}

func (e *ResourceDataSyncNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ResourceDataSyncNotFoundException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ResourceDataSyncNotFoundException) ErrorCode() string {
	return "ResourceDataSyncNotFoundException"
}
func (e *ResourceDataSyncNotFoundException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Error returned if an attempt is made to delete a patch baseline that is
// registered for a patch group.
type ResourceInUseException struct {
	Message *string
}

func (e *ResourceInUseException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ResourceInUseException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ResourceInUseException) ErrorCode() string             { return "ResourceInUseException" }
func (e *ResourceInUseException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Error returned when the caller has exceeded the default resource quotas. For
// example, too many maintenance windows or patch baselines have been created. For
// information about resource quotas in Systems Manager, see Systems Manager
// service quotas
// (http://docs.aws.amazon.com/general/latest/gr/ssm.html#limits_ssm) in the AWS
// General Reference.
type ResourceLimitExceededException struct {
	Message *string
}

func (e *ResourceLimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ResourceLimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ResourceLimitExceededException) ErrorCode() string             { return "ResourceLimitExceededException" }
func (e *ResourceLimitExceededException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified service setting was not found. Either the service name or the
// setting has not been provisioned by the AWS service team.
type ServiceSettingNotFound struct {
	Message *string
}

func (e *ServiceSettingNotFound) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *ServiceSettingNotFound) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *ServiceSettingNotFound) ErrorCode() string             { return "ServiceSettingNotFound" }
func (e *ServiceSettingNotFound) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The updated status is the same as the current status.
type StatusUnchanged struct {
	Message *string
}

func (e *StatusUnchanged) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *StatusUnchanged) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *StatusUnchanged) ErrorCode() string             { return "StatusUnchanged" }
func (e *StatusUnchanged) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The sub-type count exceeded the limit for the inventory type.
type SubTypeCountLimitExceededException struct {
	Message *string
}

func (e *SubTypeCountLimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *SubTypeCountLimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *SubTypeCountLimitExceededException) ErrorCode() string {
	return "SubTypeCountLimitExceededException"
}
func (e *SubTypeCountLimitExceededException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// You specified the Safe option for the DeregisterTargetFromMaintenanceWindow
// operation, but the target is still referenced in a task.
type TargetInUseException struct {
	Message *string
}

func (e *TargetInUseException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *TargetInUseException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *TargetInUseException) ErrorCode() string             { return "TargetInUseException" }
func (e *TargetInUseException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The specified target instance for the session is not fully configured for use
// with Session Manager. For more information, see Getting started with Session
// Manager
// (https://docs.aws.amazon.com/systems-manager/latest/userguide/session-manager-getting-started.html)
// in the AWS Systems Manager User Guide. This error is also returned if you
// attempt to start a session on an instance that is located in a different account
// or Region
type TargetNotConnected struct {
	Message *string
}

func (e *TargetNotConnected) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *TargetNotConnected) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *TargetNotConnected) ErrorCode() string             { return "TargetNotConnected" }
func (e *TargetNotConnected) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The Targets parameter includes too many tags. Remove one or more tags and try
// the command again.
type TooManyTagsError struct {
	Message *string
}

func (e *TooManyTagsError) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *TooManyTagsError) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *TooManyTagsError) ErrorCode() string             { return "TooManyTagsError" }
func (e *TooManyTagsError) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// There are concurrent updates for a resource that supports one update at a time.
type TooManyUpdates struct {
	Message *string
}

func (e *TooManyUpdates) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *TooManyUpdates) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *TooManyUpdates) ErrorCode() string             { return "TooManyUpdates" }
func (e *TooManyUpdates) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The size of inventory data has exceeded the total size limit for the resource.
type TotalSizeLimitExceededException struct {
	Message *string
}

func (e *TotalSizeLimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *TotalSizeLimitExceededException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *TotalSizeLimitExceededException) ErrorCode() string {
	return "TotalSizeLimitExceededException"
}
func (e *TotalSizeLimitExceededException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The calendar entry contained in the specified Systems Manager document is not
// supported.
type UnsupportedCalendarException struct {
	Message *string
}

func (e *UnsupportedCalendarException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *UnsupportedCalendarException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *UnsupportedCalendarException) ErrorCode() string             { return "UnsupportedCalendarException" }
func (e *UnsupportedCalendarException) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// Microsoft application patching is only available on EC2 instances and advanced
// instances. To patch Microsoft applications on on-premises servers and VMs, you
// must enable advanced instances. For more information, see Using the
// advanced-instances tier
// (https://docs.aws.amazon.com/systems-manager/latest/userguide/systems-manager-managedinstances-advanced.html)
// in the AWS Systems Manager User Guide.
type UnsupportedFeatureRequiredException struct {
	Message *string
}

func (e *UnsupportedFeatureRequiredException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *UnsupportedFeatureRequiredException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *UnsupportedFeatureRequiredException) ErrorCode() string {
	return "UnsupportedFeatureRequiredException"
}
func (e *UnsupportedFeatureRequiredException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The Context attribute that you specified for the InventoryItem is not allowed
// for this inventory type. You can only use the Context attribute with inventory
// types like AWS:ComplianceItem.
type UnsupportedInventoryItemContextException struct {
	Message *string

	TypeName *string
}

func (e *UnsupportedInventoryItemContextException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *UnsupportedInventoryItemContextException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *UnsupportedInventoryItemContextException) ErrorCode() string {
	return "UnsupportedInventoryItemContextException"
}
func (e *UnsupportedInventoryItemContextException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// Inventory item type schema version has to match supported versions in the
// service. Check output of GetInventorySchema to see the available schema version
// for each type.
type UnsupportedInventorySchemaVersionException struct {
	Message *string
}

func (e *UnsupportedInventorySchemaVersionException) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *UnsupportedInventorySchemaVersionException) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *UnsupportedInventorySchemaVersionException) ErrorCode() string {
	return "UnsupportedInventorySchemaVersionException"
}
func (e *UnsupportedInventorySchemaVersionException) ErrorFault() smithy.ErrorFault {
	return smithy.FaultClient
}

// The operating systems you specified is not supported, or the operation is not
// supported for the operating system.
type UnsupportedOperatingSystem struct {
	Message *string
}

func (e *UnsupportedOperatingSystem) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *UnsupportedOperatingSystem) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *UnsupportedOperatingSystem) ErrorCode() string             { return "UnsupportedOperatingSystem" }
func (e *UnsupportedOperatingSystem) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The parameter type is not supported.
type UnsupportedParameterType struct {
	Message *string
}

func (e *UnsupportedParameterType) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *UnsupportedParameterType) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *UnsupportedParameterType) ErrorCode() string             { return "UnsupportedParameterType" }
func (e *UnsupportedParameterType) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

// The document does not support the platform type of the given instance ID(s). For
// example, you sent an document for a Windows instance to a Linux instance.
type UnsupportedPlatformType struct {
	Message *string
}

func (e *UnsupportedPlatformType) Error() string {
	return fmt.Sprintf("%s: %s", e.ErrorCode(), e.ErrorMessage())
}
func (e *UnsupportedPlatformType) ErrorMessage() string {
	if e.Message == nil {
		return ""
	}
	return *e.Message
}
func (e *UnsupportedPlatformType) ErrorCode() string             { return "UnsupportedPlatformType" }
func (e *UnsupportedPlatformType) ErrorFault() smithy.ErrorFault { return smithy.FaultClient }

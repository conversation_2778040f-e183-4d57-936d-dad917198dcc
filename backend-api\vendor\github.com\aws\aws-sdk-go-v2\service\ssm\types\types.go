// Code generated by smithy-go-codegen DO NOT EDIT.

package types

import (
	"time"
)

// Information includes the AWS account ID where the current document is shared and
// the version shared with that account.
type AccountSharingInfo struct {

	// The AWS account ID where the current document is shared.
	AccountId *string

	// The version of the current document shared with the account.
	SharedDocumentVersion *string
}

// An activation registers one or more on-premises servers or virtual machines
// (VMs) with AWS so that you can configure those servers or VMs using Run Command.
// A server or VM that has been registered with AWS is called a managed instance.
type Activation struct {

	// The ID created by Systems Manager when you submitted the activation.
	ActivationId *string

	// The date the activation was created.
	CreatedDate *time.Time

	// A name for the managed instance when it is created.
	DefaultInstanceName *string

	// A user defined description of the activation.
	Description *string

	// The date when this activation can no longer be used to register managed
	// instances.
	ExpirationDate *time.Time

	// Whether or not the activation is expired.
	Expired bool

	// The Amazon Identity and Access Management (IAM) role to assign to the managed
	// instance.
	IamRole *string

	// The maximum number of managed instances that can be registered using this
	// activation.
	RegistrationLimit int32

	// The number of managed instances already registered with this activation.
	RegistrationsCount int32

	// Tags assigned to the activation.
	Tags []Tag
}

// Describes an association of a Systems Manager document and an instance.
type Association struct {

	// The ID created by the system when you create an association. An association is a
	// binding between a document and a set of targets with a schedule.
	AssociationId *string

	// The association name.
	AssociationName *string

	// The association version.
	AssociationVersion *string

	// The version of the document used in the association.
	DocumentVersion *string

	// The ID of the instance.
	InstanceId *string

	// The date on which the association was last run.
	LastExecutionDate *time.Time

	// The name of the Systems Manager document.
	Name *string

	// Information about the association.
	Overview *AssociationOverview

	// A cron expression that specifies a schedule when the association runs.
	ScheduleExpression *string

	// The instances targeted by the request to create an association.
	Targets []Target
}

// Describes the parameters for a document.
type AssociationDescription struct {

	// By default, when you create a new associations, the system runs it immediately
	// after it is created and then according to the schedule you specified. Specify
	// this option if you don't want an association to run immediately after you create
	// it. This parameter is not supported for rate expressions.
	ApplyOnlyAtCronInterval bool

	// The association ID.
	AssociationId *string

	// The association name.
	AssociationName *string

	// The association version.
	AssociationVersion *string

	// Specify the target for the association. This target is required for associations
	// that use an Automation document and target resources by using rate controls.
	AutomationTargetParameterName *string

	// The severity level that is assigned to the association.
	ComplianceSeverity AssociationComplianceSeverity

	// The date when the association was made.
	Date *time.Time

	// The document version.
	DocumentVersion *string

	// The ID of the instance.
	InstanceId *string

	// The date on which the association was last run.
	LastExecutionDate *time.Time

	// The last date on which the association was successfully run.
	LastSuccessfulExecutionDate *time.Time

	// The date when the association was last updated.
	LastUpdateAssociationDate *time.Time

	// The maximum number of targets allowed to run the association at the same time.
	// You can specify a number, for example 10, or a percentage of the target set, for
	// example 10%. The default value is 100%, which means all targets run the
	// association at the same time. If a new instance starts and attempts to run an
	// association while Systems Manager is running MaxConcurrency associations, the
	// association is allowed to run. During the next association interval, the new
	// instance will process its association within the limit specified for
	// MaxConcurrency.
	MaxConcurrency *string

	// The number of errors that are allowed before the system stops sending requests
	// to run the association on additional targets. You can specify either an absolute
	// number of errors, for example 10, or a percentage of the target set, for example
	// 10%. If you specify 3, for example, the system stops sending requests when the
	// fourth error is received. If you specify 0, then the system stops sending
	// requests after the first error is returned. If you run an association on 50
	// instances and set MaxError to 10%, then the system stops sending the request
	// when the sixth error is received. Executions that are already running an
	// association when MaxErrors is reached are allowed to complete, but some of these
	// executions may fail as well. If you need to ensure that there won't be more than
	// max-errors failed executions, set MaxConcurrency to 1 so that executions proceed
	// one at a time.
	MaxErrors *string

	// The name of the Systems Manager document.
	Name *string

	// An S3 bucket where you want to store the output details of the request.
	OutputLocation *InstanceAssociationOutputLocation

	// Information about the association.
	Overview *AssociationOverview

	// A description of the parameters for a document.
	Parameters map[string][]string

	// A cron expression that specifies a schedule when the association runs.
	ScheduleExpression *string

	// The association status.
	Status *AssociationStatus

	// The mode for generating association compliance. You can specify AUTO or MANUAL.
	// In AUTO mode, the system uses the status of the association execution to
	// determine the compliance status. If the association execution runs successfully,
	// then the association is COMPLIANT. If the association execution doesn't run
	// successfully, the association is NON-COMPLIANT. In MANUAL mode, you must specify
	// the AssociationId as a parameter for the PutComplianceItems API action. In this
	// case, compliance data is not managed by State Manager. It is managed by your
	// direct call to the PutComplianceItems API action. By default, all associations
	// use AUTO mode.
	SyncCompliance AssociationSyncCompliance

	// The combination of AWS Regions and AWS accounts where you want to run the
	// association.
	TargetLocations []TargetLocation

	// The instances targeted by the request.
	Targets []Target
}

// Includes information about the specified association.
type AssociationExecution struct {

	// The association ID.
	AssociationId *string

	// The association version.
	AssociationVersion *string

	// The time the execution started.
	CreatedTime *time.Time

	// Detailed status information about the execution.
	DetailedStatus *string

	// The execution ID for the association.
	ExecutionId *string

	// The date of the last execution.
	LastExecutionDate *time.Time

	// An aggregate status of the resources in the execution based on the status type.
	ResourceCountByStatus *string

	// The status of the association execution.
	Status *string
}

// Filters used in the request.
type AssociationExecutionFilter struct {

	// The key value used in the request.
	//
	// This member is required.
	Key AssociationExecutionFilterKey

	// The filter type specified in the request.
	//
	// This member is required.
	Type AssociationFilterOperatorType

	// The value specified for the key.
	//
	// This member is required.
	Value *string
}

// Includes information about the specified association execution.
type AssociationExecutionTarget struct {

	// The association ID.
	AssociationId *string

	// The association version.
	AssociationVersion *string

	// Detailed information about the execution status.
	DetailedStatus *string

	// The execution ID.
	ExecutionId *string

	// The date of the last execution.
	LastExecutionDate *time.Time

	// The location where the association details are saved.
	OutputSource *OutputSource

	// The resource ID, for example, the instance ID where the association ran.
	ResourceId *string

	// The resource type, for example, instance.
	ResourceType *string

	// The association execution status.
	Status *string
}

// Filters for the association execution.
type AssociationExecutionTargetsFilter struct {

	// The key value used in the request.
	//
	// This member is required.
	Key AssociationExecutionTargetsFilterKey

	// The value specified for the key.
	//
	// This member is required.
	Value *string
}

// Describes a filter.
type AssociationFilter struct {

	// The name of the filter. InstanceId has been deprecated.
	//
	// This member is required.
	Key AssociationFilterKey

	// The filter value.
	//
	// This member is required.
	Value *string
}

// Information about the association.
type AssociationOverview struct {

	// Returns the number of targets for the association status. For example, if you
	// created an association with two instances, and one of them was successful, this
	// would return the count of instances by status.
	AssociationStatusAggregatedCount map[string]int32

	// A detailed status of the association.
	DetailedStatus *string

	// The status of the association. Status can be: Pending, Success, or Failed.
	Status *string
}

// Describes an association status.
type AssociationStatus struct {

	// The date when the status changed.
	//
	// This member is required.
	Date *time.Time

	// The reason for the status.
	//
	// This member is required.
	Message *string

	// The status.
	//
	// This member is required.
	Name AssociationStatusName

	// A user-defined string.
	AdditionalInfo *string
}

// Information about the association version.
type AssociationVersionInfo struct {

	// By default, when you create a new associations, the system runs it immediately
	// after it is created and then according to the schedule you specified. Specify
	// this option if you don't want an association to run immediately after you create
	// it. This parameter is not supported for rate expressions.
	ApplyOnlyAtCronInterval bool

	// The ID created by the system when the association was created.
	AssociationId *string

	// The name specified for the association version when the association version was
	// created.
	AssociationName *string

	// The association version.
	AssociationVersion *string

	// The severity level that is assigned to the association.
	ComplianceSeverity AssociationComplianceSeverity

	// The date the association version was created.
	CreatedDate *time.Time

	// The version of a Systems Manager document used when the association version was
	// created.
	DocumentVersion *string

	// The maximum number of targets allowed to run the association at the same time.
	// You can specify a number, for example 10, or a percentage of the target set, for
	// example 10%. The default value is 100%, which means all targets run the
	// association at the same time. If a new instance starts and attempts to run an
	// association while Systems Manager is running MaxConcurrency associations, the
	// association is allowed to run. During the next association interval, the new
	// instance will process its association within the limit specified for
	// MaxConcurrency.
	MaxConcurrency *string

	// The number of errors that are allowed before the system stops sending requests
	// to run the association on additional targets. You can specify either an absolute
	// number of errors, for example 10, or a percentage of the target set, for example
	// 10%. If you specify 3, for example, the system stops sending requests when the
	// fourth error is received. If you specify 0, then the system stops sending
	// requests after the first error is returned. If you run an association on 50
	// instances and set MaxError to 10%, then the system stops sending the request
	// when the sixth error is received. Executions that are already running an
	// association when MaxErrors is reached are allowed to complete, but some of these
	// executions may fail as well. If you need to ensure that there won't be more than
	// max-errors failed executions, set MaxConcurrency to 1 so that executions proceed
	// one at a time.
	MaxErrors *string

	// The name specified when the association was created.
	Name *string

	// The location in Amazon S3 specified for the association when the association
	// version was created.
	OutputLocation *InstanceAssociationOutputLocation

	// Parameters specified when the association version was created.
	Parameters map[string][]string

	// The cron or rate schedule specified for the association when the association
	// version was created.
	ScheduleExpression *string

	// The mode for generating association compliance. You can specify AUTO or MANUAL.
	// In AUTO mode, the system uses the status of the association execution to
	// determine the compliance status. If the association execution runs successfully,
	// then the association is COMPLIANT. If the association execution doesn't run
	// successfully, the association is NON-COMPLIANT. In MANUAL mode, you must specify
	// the AssociationId as a parameter for the PutComplianceItems API action. In this
	// case, compliance data is not managed by State Manager. It is managed by your
	// direct call to the PutComplianceItems API action. By default, all associations
	// use AUTO mode.
	SyncCompliance AssociationSyncCompliance

	// The combination of AWS Regions and AWS accounts where you wanted to run the
	// association when this association version was created.
	TargetLocations []TargetLocation

	// The targets specified for the association when the association version was
	// created.
	Targets []Target
}

// A structure that includes attributes that describe a document attachment.
type AttachmentContent struct {

	// The cryptographic hash value of the document content.
	Hash *string

	// The hash algorithm used to calculate the hash value.
	HashType AttachmentHashType

	// The name of an attachment.
	Name *string

	// The size of an attachment in bytes.
	Size int64

	// The URL location of the attachment content.
	Url *string
}

// An attribute of an attachment, such as the attachment name.
type AttachmentInformation struct {

	// The name of the attachment.
	Name *string
}

// Identifying information about a document attachment, including the file name and
// a key-value pair that identifies the location of an attachment to a document.
type AttachmentsSource struct {

	// The key of a key-value pair that identifies the location of an attachment to a
	// document.
	Key AttachmentsSourceKey

	// The name of the document attachment file.
	Name *string

	// The value of a key-value pair that identifies the location of an attachment to a
	// document. The format for Value depends on the type of key you specify.
	//
	// * For
	// the key SourceUrl, the value is an S3 bucket location. For example: "Values": [
	// "s3://doc-example-bucket/my-folder" ]
	//
	// * For the key S3FileUrl, the value is a
	// file in an S3 bucket. For example: "Values": [
	// "s3://doc-example-bucket/my-folder/my-file.py" ]
	//
	// * For the key
	// AttachmentReference, the value is constructed from the name of another SSM
	// document in your account, a version number of that document, and a file attached
	// to that document version that you want to reuse. For example: "Values": [
	// "MyOtherDocument/3/my-other-file.py" ] However, if the SSM document is shared
	// with you from another account, the full SSM document ARN must be specified
	// instead of the document name only. For example: "Values": [
	// "arn:aws:ssm:us-east-2:************:document/OtherAccountDocument/3/their-file.py"
	// ]
	Values []string
}

// Detailed information about the current state of an individual Automation
// execution.
type AutomationExecution struct {

	// The ID of a State Manager association used in the Automation operation.
	AssociationId *string

	// The execution ID.
	AutomationExecutionId *string

	// The execution status of the Automation.
	AutomationExecutionStatus AutomationExecutionStatus

	// The subtype of the Automation operation. Currently, the only supported value is
	// ChangeRequest.
	AutomationSubtype AutomationSubtype

	// The name of the Change Manager change request.
	ChangeRequestName *string

	// The action of the step that is currently running.
	CurrentAction *string

	// The name of the step that is currently running.
	CurrentStepName *string

	// The name of the Automation document used during the execution.
	DocumentName *string

	// The version of the document to use during execution.
	DocumentVersion *string

	// The Amazon Resource Name (ARN) of the user who ran the automation.
	ExecutedBy *string

	// The time the execution finished.
	ExecutionEndTime *time.Time

	// The time the execution started.
	ExecutionStartTime *time.Time

	// A message describing why an execution has failed, if the status is set to
	// Failed.
	FailureMessage *string

	// The MaxConcurrency value specified by the user when the execution started.
	MaxConcurrency *string

	// The MaxErrors value specified by the user when the execution started.
	MaxErrors *string

	// The automation execution mode.
	Mode ExecutionMode

	// The ID of an OpsItem that is created to represent a Change Manager change
	// request.
	OpsItemId *string

	// The list of execution outputs as defined in the automation document.
	Outputs map[string][]string

	// The key-value map of execution parameters, which were supplied when calling
	// StartAutomationExecution.
	Parameters map[string][]string

	// The AutomationExecutionId of the parent automation.
	ParentAutomationExecutionId *string

	// An aggregate of step execution statuses displayed in the AWS Console for a
	// multi-Region and multi-account Automation execution.
	ProgressCounters *ProgressCounters

	// A list of resolved targets in the rate control execution.
	ResolvedTargets *ResolvedTargets

	// Information about the Automation runbooks (Automation documents) that are run as
	// part of a runbook workflow. The Automation runbooks specified for the runbook
	// workflow can't run until all required approvals for the change request have been
	// received.
	Runbooks []Runbook

	// The date and time the Automation operation is scheduled to start.
	ScheduledTime *time.Time

	// A list of details about the current state of all steps that comprise an
	// execution. An Automation document contains a list of steps that are run in
	// order.
	StepExecutions []StepExecution

	// A boolean value that indicates if the response contains the full list of the
	// Automation step executions. If true, use the DescribeAutomationStepExecutions
	// API action to get the full list of step executions.
	StepExecutionsTruncated bool

	// The target of the execution.
	Target *string

	// The combination of AWS Regions and/or AWS accounts where you want to run the
	// Automation.
	TargetLocations []TargetLocation

	// The specified key-value mapping of document parameters to target resources.
	TargetMaps []map[string][]string

	// The parameter name.
	TargetParameterName *string

	// The specified targets.
	Targets []Target
}

// A filter used to match specific automation executions. This is used to limit the
// scope of Automation execution information returned.
type AutomationExecutionFilter struct {

	// One or more keys to limit the results.
	//
	// This member is required.
	Key AutomationExecutionFilterKey

	// The values used to limit the execution information associated with the filter's
	// key.
	//
	// This member is required.
	Values []string
}

// Details about a specific Automation execution.
type AutomationExecutionMetadata struct {

	// The ID of a State Manager association used in the Automation operation.
	AssociationId *string

	// The execution ID.
	AutomationExecutionId *string

	// The status of the execution.
	AutomationExecutionStatus AutomationExecutionStatus

	// The subtype of the Automation operation. Currently, the only supported value is
	// ChangeRequest.
	AutomationSubtype AutomationSubtype

	// Use this filter with DescribeAutomationExecutions. Specify either Local or
	// CrossAccount. CrossAccount is an Automation that runs in multiple AWS Regions
	// and accounts. For more information, see Running Automation workflows in multiple
	// AWS Regions and accounts
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/systems-manager-automation-multiple-accounts-and-regions.html)
	// in the AWS Systems Manager User Guide.
	AutomationType AutomationType

	// The name of the Change Manager change request.
	ChangeRequestName *string

	// The action of the step that is currently running.
	CurrentAction *string

	// The name of the step that is currently running.
	CurrentStepName *string

	// The name of the Automation document used during execution.
	DocumentName *string

	// The document version used during the execution.
	DocumentVersion *string

	// The IAM role ARN of the user who ran the Automation.
	ExecutedBy *string

	// The time the execution finished. This is not populated if the execution is still
	// in progress.
	ExecutionEndTime *time.Time

	// The time the execution started.
	ExecutionStartTime *time.Time

	// The list of execution outputs as defined in the Automation document.
	FailureMessage *string

	// An S3 bucket where execution information is stored.
	LogFile *string

	// The MaxConcurrency value specified by the user when starting the Automation.
	MaxConcurrency *string

	// The MaxErrors value specified by the user when starting the Automation.
	MaxErrors *string

	// The Automation execution mode.
	Mode ExecutionMode

	// The ID of an OpsItem that is created to represent a Change Manager change
	// request.
	OpsItemId *string

	// The list of execution outputs as defined in the Automation document.
	Outputs map[string][]string

	// The ExecutionId of the parent Automation.
	ParentAutomationExecutionId *string

	// A list of targets that resolved during the execution.
	ResolvedTargets *ResolvedTargets

	// Information about the Automation runbooks (Automation documents) that are run
	// during a runbook workflow in Change Manager. The Automation runbooks specified
	// for the runbook workflow can't run until all required approvals for the change
	// request have been received.
	Runbooks []Runbook

	// The date and time the Automation operation is scheduled to start.
	ScheduledTime *time.Time

	// The list of execution outputs as defined in the Automation document.
	Target *string

	// The specified key-value mapping of document parameters to target resources.
	TargetMaps []map[string][]string

	// The list of execution outputs as defined in the Automation document.
	TargetParameterName *string

	// The targets defined by the user when starting the Automation.
	Targets []Target
}

// Defines the basic information about a patch baseline override.
type BaselineOverride struct {

	// A set of rules defining the approval rules for a patch baseline.
	ApprovalRules *PatchRuleGroup

	// A list of explicitly approved patches for the baseline. For information about
	// accepted formats for lists of approved patches and rejected patches, see About
	// package name formats for approved and rejected patch lists
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/patch-manager-approved-rejected-package-name-formats.html)
	// in the AWS Systems Manager User Guide.
	ApprovedPatches []string

	// Defines the compliance level for approved patches. When an approved patch is
	// reported as missing, this value describes the severity of the compliance
	// violation.
	ApprovedPatchesComplianceLevel PatchComplianceLevel

	// Indicates whether the list of approved patches includes non-security updates
	// that should be applied to the instances. The default value is 'false'. Applies
	// to Linux instances only.
	ApprovedPatchesEnableNonSecurity bool

	// A set of patch filters, typically used for approval rules.
	GlobalFilters *PatchFilterGroup

	// The operating system rule used by the patch baseline override.
	OperatingSystem OperatingSystem

	// A list of explicitly rejected patches for the baseline. For information about
	// accepted formats for lists of approved patches and rejected patches, see About
	// package name formats for approved and rejected patch lists
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/patch-manager-approved-rejected-package-name-formats.html)
	// in the AWS Systems Manager User Guide.
	RejectedPatches []string

	// The action for Patch Manager to take on patches included in the RejectedPackages
	// list. A patch can be allowed only if it is a dependency of another package, or
	// blocked entirely along with packages that include it as a dependency.
	RejectedPatchesAction PatchAction

	// Information about the patches to use to update the instances, including target
	// operating systems and source repositories. Applies to Linux instances only.
	Sources []PatchSource
}

// Configuration options for sending command output to CloudWatch Logs.
type CloudWatchOutputConfig struct {

	// The name of the CloudWatch log group where you want to send command output. If
	// you don't specify a group name, Systems Manager automatically creates a log
	// group for you. The log group uses the following naming format:
	// aws/ssm/SystemsManagerDocumentName.
	CloudWatchLogGroupName *string

	// Enables Systems Manager to send command output to CloudWatch Logs.
	CloudWatchOutputEnabled bool
}

// Describes a command request.
type Command struct {

	// CloudWatch Logs information where you want Systems Manager to send the command
	// output.
	CloudWatchOutputConfig *CloudWatchOutputConfig

	// A unique identifier for this command.
	CommandId *string

	// User-specified information about the command, such as a brief description of
	// what the command should do.
	Comment *string

	// The number of targets for which the command invocation reached a terminal state.
	// Terminal states include the following: Success, Failed, Execution Timed Out,
	// Delivery Timed Out, Canceled, Terminated, or Undeliverable.
	CompletedCount int32

	// The number of targets for which the status is Delivery Timed Out.
	DeliveryTimedOutCount int32

	// The name of the document requested for execution.
	DocumentName *string

	// The SSM document version.
	DocumentVersion *string

	// The number of targets for which the status is Failed or Execution Timed Out.
	ErrorCount int32

	// If this time is reached and the command has not already started running, it will
	// not run. Calculated based on the ExpiresAfter user input provided as part of the
	// SendCommand API.
	ExpiresAfter *time.Time

	// The instance IDs against which this command was requested.
	InstanceIds []string

	// The maximum number of instances that are allowed to run the command at the same
	// time. You can specify a number of instances, such as 10, or a percentage of
	// instances, such as 10%. The default value is 50. For more information about how
	// to use MaxConcurrency, see Running commands using Systems Manager Run Command
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/run-command.html)
	// in the AWS Systems Manager User Guide.
	MaxConcurrency *string

	// The maximum number of errors allowed before the system stops sending the command
	// to additional targets. You can specify a number of errors, such as 10, or a
	// percentage or errors, such as 10%. The default value is 0. For more information
	// about how to use MaxErrors, see Running commands using Systems Manager Run
	// Command
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/run-command.html)
	// in the AWS Systems Manager User Guide.
	MaxErrors *string

	// Configurations for sending notifications about command status changes.
	NotificationConfig *NotificationConfig

	// The S3 bucket where the responses to the command executions should be stored.
	// This was requested when issuing the command.
	OutputS3BucketName *string

	// The S3 directory path inside the bucket where the responses to the command
	// executions should be stored. This was requested when issuing the command.
	OutputS3KeyPrefix *string

	// (Deprecated) You can no longer specify this parameter. The system ignores it.
	// Instead, Systems Manager automatically determines the Region of the S3 bucket.
	OutputS3Region *string

	// The parameter values to be inserted in the document when running the command.
	Parameters map[string][]string

	// The date and time the command was requested.
	RequestedDateTime *time.Time

	// The IAM service role that Run Command uses to act on your behalf when sending
	// notifications about command status changes.
	ServiceRole *string

	// The status of the command.
	Status CommandStatus

	// A detailed status of the command execution. StatusDetails includes more
	// information than Status because it includes states resulting from error and
	// concurrency control parameters. StatusDetails can show different results than
	// Status. For more information about these statuses, see Understanding command
	// statuses
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/monitor-commands.html)
	// in the AWS Systems Manager User Guide. StatusDetails can be one of the following
	// values:
	//
	// * Pending: The command has not been sent to any instances.
	//
	// * In
	// Progress: The command has been sent to at least one instance but has not reached
	// a final state on all instances.
	//
	// * Success: The command successfully ran on all
	// invocations. This is a terminal state.
	//
	// * Delivery Timed Out: The value of
	// MaxErrors or more command invocations shows a status of Delivery Timed Out. This
	// is a terminal state.
	//
	// * Execution Timed Out: The value of MaxErrors or more
	// command invocations shows a status of Execution Timed Out. This is a terminal
	// state.
	//
	// * Failed: The value of MaxErrors or more command invocations shows a
	// status of Failed. This is a terminal state.
	//
	// * Incomplete: The command was
	// attempted on all instances and one or more invocations does not have a value of
	// Success but not enough invocations failed for the status to be Failed. This is a
	// terminal state.
	//
	// * Canceled: The command was terminated before it was completed.
	// This is a terminal state.
	//
	// * Rate Exceeded: The number of instances targeted by
	// the command exceeded the account limit for pending invocations. The system has
	// canceled the command before running it on any instance. This is a terminal
	// state.
	StatusDetails *string

	// The number of targets for the command.
	TargetCount int32

	// An array of search criteria that targets instances using a Key,Value combination
	// that you specify. Targets is required if you don't provide one or more instance
	// IDs in the call.
	Targets []Target

	// The TimeoutSeconds value specified for a command.
	TimeoutSeconds int32
}

// Describes a command filter. An instance ID can't be specified when a command
// status is Pending because the command hasn't run on the instance yet.
type CommandFilter struct {

	// The name of the filter.
	//
	// This member is required.
	Key CommandFilterKey

	// The filter value. Valid values for each filter key are as follows:
	//
	// *
	// InvokedAfter: Specify a timestamp to limit your results. For example, specify
	// 2018-07-07T00:00:00Z to see a list of command executions occurring July 7, 2018,
	// and later.
	//
	// * InvokedBefore: Specify a timestamp to limit your results. For
	// example, specify 2018-07-07T00:00:00Z to see a list of command executions from
	// before July 7, 2018.
	//
	// * Status: Specify a valid command status to see a list of
	// all command executions with that status. Status values you can specify
	// include:
	//
	// * Pending
	//
	// * InProgress
	//
	// * Success
	//
	// * Cancelled
	//
	// * Failed
	//
	// *
	// TimedOut
	//
	// * Cancelling
	//
	// * DocumentName: Specify name of the SSM document for
	// which you want to see command execution results. For example, specify
	// AWS-RunPatchBaseline to see command executions that used this SSM document to
	// perform security patching operations on instances.
	//
	// * ExecutionStage: Specify
	// one of the following values:
	//
	// * Executing: Returns a list of command executions
	// that are currently still running.
	//
	// * Complete: Returns a list of command
	// executions that have already completed.
	//
	// This member is required.
	Value *string
}

// An invocation is copy of a command sent to a specific instance. A command can
// apply to one or more instances. A command invocation applies to one instance.
// For example, if a user runs SendCommand against three instances, then a command
// invocation is created for each requested instance ID. A command invocation
// returns status and detail information about a command you ran.
type CommandInvocation struct {

	// CloudWatch Logs information where you want Systems Manager to send the command
	// output.
	CloudWatchOutputConfig *CloudWatchOutputConfig

	// The command against which this invocation was requested.
	CommandId *string

	CommandPlugins []CommandPlugin

	// User-specified information about the command, such as a brief description of
	// what the command should do.
	Comment *string

	// The document name that was requested for execution.
	DocumentName *string

	// The SSM document version.
	DocumentVersion *string

	// The instance ID in which this invocation was requested.
	InstanceId *string

	// The name of the invocation target. For EC2 instances this is the value for the
	// aws:Name tag. For on-premises instances, this is the name of the instance.
	InstanceName *string

	// Configurations for sending notifications about command status changes on a per
	// instance basis.
	NotificationConfig *NotificationConfig

	// The time and date the request was sent to this instance.
	RequestedDateTime *time.Time

	// The IAM service role that Run Command uses to act on your behalf when sending
	// notifications about command status changes on a per instance basis.
	ServiceRole *string

	// The URL to the plugin's StdErr file in Amazon S3, if the S3 bucket was defined
	// for the parent command. For an invocation, StandardErrorUrl is populated if
	// there is just one plugin defined for the command, and the S3 bucket was defined
	// for the command.
	StandardErrorUrl *string

	// The URL to the plugin's StdOut file in Amazon S3, if the S3 bucket was defined
	// for the parent command. For an invocation, StandardOutputUrl is populated if
	// there is just one plugin defined for the command, and the S3 bucket was defined
	// for the command.
	StandardOutputUrl *string

	// Whether or not the invocation succeeded, failed, or is pending.
	Status CommandInvocationStatus

	// A detailed status of the command execution for each invocation (each instance
	// targeted by the command). StatusDetails includes more information than Status
	// because it includes states resulting from error and concurrency control
	// parameters. StatusDetails can show different results than Status. For more
	// information about these statuses, see Understanding command statuses
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/monitor-commands.html)
	// in the AWS Systems Manager User Guide. StatusDetails can be one of the following
	// values:
	//
	// * Pending: The command has not been sent to the instance.
	//
	// * In
	// Progress: The command has been sent to the instance but has not reached a
	// terminal state.
	//
	// * Success: The execution of the command or plugin was
	// successfully completed. This is a terminal state.
	//
	// * Delivery Timed Out: The
	// command was not delivered to the instance before the delivery timeout expired.
	// Delivery timeouts do not count against the parent command's MaxErrors limit, but
	// they do contribute to whether the parent command status is Success or
	// Incomplete. This is a terminal state.
	//
	// * Execution Timed Out: Command execution
	// started on the instance, but the execution was not complete before the execution
	// timeout expired. Execution timeouts count against the MaxErrors limit of the
	// parent command. This is a terminal state.
	//
	// * Failed: The command was not
	// successful on the instance. For a plugin, this indicates that the result code
	// was not zero. For a command invocation, this indicates that the result code for
	// one or more plugins was not zero. Invocation failures count against the
	// MaxErrors limit of the parent command. This is a terminal state.
	//
	// * Canceled:
	// The command was terminated before it was completed. This is a terminal state.
	//
	// *
	// Undeliverable: The command can't be delivered to the instance. The instance
	// might not exist or might not be responding. Undeliverable invocations don't
	// count against the parent command's MaxErrors limit and don't contribute to
	// whether the parent command status is Success or Incomplete. This is a terminal
	// state.
	//
	// * Terminated: The parent command exceeded its MaxErrors limit and
	// subsequent command invocations were canceled by the system. This is a terminal
	// state.
	StatusDetails *string

	// Gets the trace output sent by the agent.
	TraceOutput *string
}

// Describes plugin details.
type CommandPlugin struct {

	// The name of the plugin. Must be one of the following: aws:updateAgent,
	// aws:domainjoin, aws:applications, aws:runPowerShellScript, aws:psmodule,
	// aws:cloudWatch, aws:runShellScript, or aws:updateSSMAgent.
	Name *string

	// Output of the plugin execution.
	Output *string

	// The S3 bucket where the responses to the command executions should be stored.
	// This was requested when issuing the command. For example, in the following
	// response:
	// doc-example-bucket/ab19cb99-a030-46dd-9dfc-8eSAMPLEPre-Fix/i-02573cafcfEXAMPLE/awsrunShellScript
	// doc-example-bucket is the name of the S3 bucket;
	// ab19cb99-a030-46dd-9dfc-8eSAMPLEPre-Fix is the name of the S3 prefix;
	// i-02573cafcfEXAMPLE is the instance ID; awsrunShellScript is the name of the
	// plugin.
	OutputS3BucketName *string

	// The S3 directory path inside the bucket where the responses to the command
	// executions should be stored. This was requested when issuing the command. For
	// example, in the following response:
	// doc-example-bucket/ab19cb99-a030-46dd-9dfc-8eSAMPLEPre-Fix/i-02573cafcfEXAMPLE/awsrunShellScript
	// doc-example-bucket is the name of the S3 bucket;
	// ab19cb99-a030-46dd-9dfc-8eSAMPLEPre-Fix is the name of the S3 prefix;
	// i-02573cafcfEXAMPLE is the instance ID; awsrunShellScript is the name of the
	// plugin.
	OutputS3KeyPrefix *string

	// (Deprecated) You can no longer specify this parameter. The system ignores it.
	// Instead, Systems Manager automatically determines the S3 bucket region.
	OutputS3Region *string

	// A numeric response code generated after running the plugin.
	ResponseCode int32

	// The time the plugin stopped running. Could stop prematurely if, for example, a
	// cancel command was sent.
	ResponseFinishDateTime *time.Time

	// The time the plugin started running.
	ResponseStartDateTime *time.Time

	// The URL for the complete text written by the plugin to stderr. If execution is
	// not yet complete, then this string is empty.
	StandardErrorUrl *string

	// The URL for the complete text written by the plugin to stdout in Amazon S3. If
	// the S3 bucket for the command was not specified, then this string is empty.
	StandardOutputUrl *string

	// The status of this plugin. You can run a document with multiple plugins.
	Status CommandPluginStatus

	// A detailed status of the plugin execution. StatusDetails includes more
	// information than Status because it includes states resulting from error and
	// concurrency control parameters. StatusDetails can show different results than
	// Status. For more information about these statuses, see Understanding command
	// statuses
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/monitor-commands.html)
	// in the AWS Systems Manager User Guide. StatusDetails can be one of the following
	// values:
	//
	// * Pending: The command has not been sent to the instance.
	//
	// * In
	// Progress: The command has been sent to the instance but has not reached a
	// terminal state.
	//
	// * Success: The execution of the command or plugin was
	// successfully completed. This is a terminal state.
	//
	// * Delivery Timed Out: The
	// command was not delivered to the instance before the delivery timeout expired.
	// Delivery timeouts do not count against the parent command's MaxErrors limit, but
	// they do contribute to whether the parent command status is Success or
	// Incomplete. This is a terminal state.
	//
	// * Execution Timed Out: Command execution
	// started on the instance, but the execution was not complete before the execution
	// timeout expired. Execution timeouts count against the MaxErrors limit of the
	// parent command. This is a terminal state.
	//
	// * Failed: The command was not
	// successful on the instance. For a plugin, this indicates that the result code
	// was not zero. For a command invocation, this indicates that the result code for
	// one or more plugins was not zero. Invocation failures count against the
	// MaxErrors limit of the parent command. This is a terminal state.
	//
	// * Canceled:
	// The command was terminated before it was completed. This is a terminal state.
	//
	// *
	// Undeliverable: The command can't be delivered to the instance. The instance
	// might not exist, or it might not be responding. Undeliverable invocations don't
	// count against the parent command's MaxErrors limit, and they don't contribute to
	// whether the parent command status is Success or Incomplete. This is a terminal
	// state.
	//
	// * Terminated: The parent command exceeded its MaxErrors limit and
	// subsequent command invocations were canceled by the system. This is a terminal
	// state.
	StatusDetails *string
}

// A summary of the call execution that includes an execution ID, the type of
// execution (for example, Command), and the date/time of the execution using a
// datetime object that is saved in the following format: yyyy-MM-dd'T'HH:mm:ss'Z'.
type ComplianceExecutionSummary struct {

	// The time the execution ran as a datetime object that is saved in the following
	// format: yyyy-MM-dd'T'HH:mm:ss'Z'.
	//
	// This member is required.
	ExecutionTime *time.Time

	// An ID created by the system when PutComplianceItems was called. For example,
	// CommandID is a valid execution ID. You can use this ID in subsequent calls.
	ExecutionId *string

	// The type of execution. For example, Command is a valid execution type.
	ExecutionType *string
}

// Information about the compliance as defined by the resource type. For example,
// for a patch resource type, Items includes information about the PatchSeverity,
// Classification, and so on.
type ComplianceItem struct {

	// The compliance type. For example, Association (for a State Manager association),
	// Patch, or Custom:string are all valid compliance types.
	ComplianceType *string

	// A "Key": "Value" tag combination for the compliance item.
	Details map[string]string

	// A summary for the compliance item. The summary includes an execution ID, the
	// execution type (for example, command), and the execution time.
	ExecutionSummary *ComplianceExecutionSummary

	// An ID for the compliance item. For example, if the compliance item is a Windows
	// patch, the ID could be the number of the KB article; for example: KB4010320.
	Id *string

	// An ID for the resource. For a managed instance, this is the instance ID.
	ResourceId *string

	// The type of resource. ManagedInstance is currently the only supported resource
	// type.
	ResourceType *string

	// The severity of the compliance status. Severity can be one of the following:
	// Critical, High, Medium, Low, Informational, Unspecified.
	Severity ComplianceSeverity

	// The status of the compliance item. An item is either COMPLIANT, NON_COMPLIANT,
	// or an empty string (for Windows patches that aren't applicable).
	Status ComplianceStatus

	// A title for the compliance item. For example, if the compliance item is a
	// Windows patch, the title could be the title of the KB article for the patch; for
	// example: Security Update for Active Directory Federation Services.
	Title *string
}

// Information about a compliance item.
type ComplianceItemEntry struct {

	// The severity of the compliance status. Severity can be one of the following:
	// Critical, High, Medium, Low, Informational, Unspecified.
	//
	// This member is required.
	Severity ComplianceSeverity

	// The status of the compliance item. An item is either COMPLIANT or NON_COMPLIANT.
	//
	// This member is required.
	Status ComplianceStatus

	// A "Key": "Value" tag combination for the compliance item.
	Details map[string]string

	// The compliance item ID. For example, if the compliance item is a Windows patch,
	// the ID could be the number of the KB article.
	Id *string

	// The title of the compliance item. For example, if the compliance item is a
	// Windows patch, the title could be the title of the KB article for the patch; for
	// example: Security Update for Active Directory Federation Services.
	Title *string
}

// One or more filters. Use a filter to return a more specific list of results.
type ComplianceStringFilter struct {

	// The name of the filter.
	Key *string

	// The type of comparison that should be performed for the value: Equal, NotEqual,
	// BeginWith, LessThan, or GreaterThan.
	Type ComplianceQueryOperatorType

	// The value for which to search.
	Values []string
}

// A summary of compliance information by compliance type.
type ComplianceSummaryItem struct {

	// The type of compliance item. For example, the compliance type can be
	// Association, Patch, or Custom:string.
	ComplianceType *string

	// A list of COMPLIANT items for the specified compliance type.
	CompliantSummary *CompliantSummary

	// A list of NON_COMPLIANT items for the specified compliance type.
	NonCompliantSummary *NonCompliantSummary
}

// A summary of resources that are compliant. The summary is organized according to
// the resource count for each compliance type.
type CompliantSummary struct {

	// The total number of resources that are compliant.
	CompliantCount int32

	// A summary of the compliance severity by compliance type.
	SeveritySummary *SeveritySummary
}

// Describes the association of a Systems Manager SSM document and an instance.
type CreateAssociationBatchRequestEntry struct {

	// The name of the SSM document that contains the configuration information for the
	// instance. You can specify Command or Automation documents. You can specify
	// AWS-predefined documents, documents you created, or a document that is shared
	// with you from another account. For SSM documents that are shared with you from
	// other AWS accounts, you must specify the complete SSM document ARN, in the
	// following format: arn:aws:ssm:region:account-id:document/document-name  For
	// example: arn:aws:ssm:us-east-2:***********:document/My-Shared-Document For
	// AWS-predefined documents and SSM documents you created in your account, you only
	// need to specify the document name. For example, AWS-ApplyPatchBaseline or
	// My-Document.
	//
	// This member is required.
	Name *string

	// By default, when you create a new associations, the system runs it immediately
	// after it is created and then according to the schedule you specified. Specify
	// this option if you don't want an association to run immediately after you create
	// it. This parameter is not supported for rate expressions.
	ApplyOnlyAtCronInterval bool

	// Specify a descriptive name for the association.
	AssociationName *string

	// Specify the target for the association. This target is required for associations
	// that use an Automation document and target resources by using rate controls.
	AutomationTargetParameterName *string

	// The severity level to assign to the association.
	ComplianceSeverity AssociationComplianceSeverity

	// The document version.
	DocumentVersion *string

	// The ID of the instance.
	InstanceId *string

	// The maximum number of targets allowed to run the association at the same time.
	// You can specify a number, for example 10, or a percentage of the target set, for
	// example 10%. The default value is 100%, which means all targets run the
	// association at the same time. If a new instance starts and attempts to run an
	// association while Systems Manager is running MaxConcurrency associations, the
	// association is allowed to run. During the next association interval, the new
	// instance will process its association within the limit specified for
	// MaxConcurrency.
	MaxConcurrency *string

	// The number of errors that are allowed before the system stops sending requests
	// to run the association on additional targets. You can specify either an absolute
	// number of errors, for example 10, or a percentage of the target set, for example
	// 10%. If you specify 3, for example, the system stops sending requests when the
	// fourth error is received. If you specify 0, then the system stops sending
	// requests after the first error is returned. If you run an association on 50
	// instances and set MaxError to 10%, then the system stops sending the request
	// when the sixth error is received. Executions that are already running an
	// association when MaxErrors is reached are allowed to complete, but some of these
	// executions may fail as well. If you need to ensure that there won't be more than
	// max-errors failed executions, set MaxConcurrency to 1 so that executions proceed
	// one at a time.
	MaxErrors *string

	// An S3 bucket where you want to store the results of this request.
	OutputLocation *InstanceAssociationOutputLocation

	// A description of the parameters for a document.
	Parameters map[string][]string

	// A cron expression that specifies a schedule when the association runs.
	ScheduleExpression *string

	// The mode for generating association compliance. You can specify AUTO or MANUAL.
	// In AUTO mode, the system uses the status of the association execution to
	// determine the compliance status. If the association execution runs successfully,
	// then the association is COMPLIANT. If the association execution doesn't run
	// successfully, the association is NON-COMPLIANT. In MANUAL mode, you must specify
	// the AssociationId as a parameter for the PutComplianceItems API action. In this
	// case, compliance data is not managed by State Manager. It is managed by your
	// direct call to the PutComplianceItems API action. By default, all associations
	// use AUTO mode.
	SyncCompliance AssociationSyncCompliance

	// Use this action to create an association in multiple Regions and multiple
	// accounts.
	TargetLocations []TargetLocation

	// The instances targeted by the request.
	Targets []Target
}

// Filter for the DescribeActivation API.
type DescribeActivationsFilter struct {

	// The name of the filter.
	FilterKey DescribeActivationsFilterKeys

	// The filter values.
	FilterValues []string
}

// A default version of a document.
type DocumentDefaultVersionDescription struct {

	// The default version of the document.
	DefaultVersion *string

	// The default version of the artifact associated with the document.
	DefaultVersionName *string

	// The name of the document.
	Name *string
}

// Describes a Systems Manager document.
type DocumentDescription struct {

	// The version of the document currently approved for use in the organization.
	ApprovedVersion *string

	// Details about the document attachments, including names, locations, sizes, and
	// so on.
	AttachmentsInformation []AttachmentInformation

	// The user in your organization who created the document.
	Author *string

	// The date when the document was created.
	CreatedDate *time.Time

	// The default version.
	DefaultVersion *string

	// A description of the document.
	Description *string

	// The document format, either JSON or YAML.
	DocumentFormat DocumentFormat

	// The type of document.
	DocumentType DocumentType

	// The document version.
	DocumentVersion *string

	// The Sha256 or Sha1 hash created by the system when the document was created.
	// Sha1 hashes have been deprecated.
	Hash *string

	// The hash type of the document. Valid values include Sha256 or Sha1. Sha1 hashes
	// have been deprecated.
	HashType DocumentHashType

	// The latest version of the document.
	LatestVersion *string

	// The name of the Systems Manager document.
	Name *string

	// The AWS user account that created the document.
	Owner *string

	// A description of the parameters for a document.
	Parameters []DocumentParameter

	// The version of the document that is currently under review.
	PendingReviewVersion *string

	// The list of OS platforms compatible with this Systems Manager document.
	PlatformTypes []PlatformType

	// A list of SSM documents required by a document. For example, an
	// ApplicationConfiguration document requires an ApplicationConfigurationSchema
	// document.
	Requires []DocumentRequires

	// Details about the review of a document.
	ReviewInformation []ReviewInformation

	// The current status of the review.
	ReviewStatus ReviewStatus

	// The schema version.
	SchemaVersion *string

	// The SHA1 hash of the document, which you can use for verification.
	Sha1 *string

	// The status of the Systems Manager document.
	Status DocumentStatus

	// A message returned by AWS Systems Manager that explains the Status value. For
	// example, a Failed status might be explained by the StatusInformation message,
	// "The specified S3 bucket does not exist. Verify that the URL of the S3 bucket is
	// correct."
	StatusInformation *string

	// The tags, or metadata, that have been applied to the document.
	Tags []Tag

	// The target type which defines the kinds of resources the document can run on.
	// For example, /AWS::EC2::Instance. For a list of valid resource types, see AWS
	// resource and property types reference
	// (http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-template-resource-type-ref.html)
	// in the AWS CloudFormation User Guide.
	TargetType *string

	// The version of the artifact associated with the document.
	VersionName *string
}

// This data type is deprecated. Instead, use DocumentKeyValuesFilter.
type DocumentFilter struct {

	// The name of the filter.
	//
	// This member is required.
	Key DocumentFilterKey

	// The value of the filter.
	//
	// This member is required.
	Value *string
}

// Describes the name of a Systems Manager document.
type DocumentIdentifier struct {

	// The user in your organization who created the document.
	Author *string

	// The document format, either JSON or YAML.
	DocumentFormat DocumentFormat

	// The document type.
	DocumentType DocumentType

	// The document version.
	DocumentVersion *string

	// The name of the Systems Manager document.
	Name *string

	// The AWS user account that created the document.
	Owner *string

	// The operating system platform.
	PlatformTypes []PlatformType

	// A list of SSM documents required by a document. For example, an
	// ApplicationConfiguration document requires an ApplicationConfigurationSchema
	// document.
	Requires []DocumentRequires

	// The current status of a document review.
	ReviewStatus ReviewStatus

	// The schema version.
	SchemaVersion *string

	// The tags, or metadata, that have been applied to the document.
	Tags []Tag

	// The target type which defines the kinds of resources the document can run on.
	// For example, /AWS::EC2::Instance. For a list of valid resource types, see AWS
	// resource and property types reference
	// (http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-template-resource-type-ref.html)
	// in the AWS CloudFormation User Guide.
	TargetType *string

	// An optional field specifying the version of the artifact associated with the
	// document. For example, "Release 12, Update 6". This value is unique across all
	// versions of a document, and cannot be changed.
	VersionName *string
}

// One or more filters. Use a filter to return a more specific list of documents.
// For keys, you can specify one or more tags that have been applied to a document.
// You can also use AWS-provided keys, some of which have specific allowed values.
// These keys and their associated values are as follows: DocumentType
//
// *
// ApplicationConfiguration
//
// * ApplicationConfigurationSchema
//
// * Automation
//
// *
// ChangeCalendar
//
// * Command
//
// * DeploymentStrategy
//
// * Package
//
// * Policy
//
// *
// Session
//
// Owner Note that only one Owner can be specified in a request. For
// example: Key=Owner,Values=Self.
//
// * Amazon
//
// * Private
//
// * Public
//
// * Self
//
// *
// ThirdParty
//
// PlatformTypes
//
// * Linux
//
// * Windows
//
// Name is another AWS-provided key.
// If you use Name as a key, you can use a name prefix to return a list of
// documents. For example, in the AWS CLI, to return a list of all documents that
// begin with Te, run the following command: aws ssm list-documents --filters
// Key=Name,Values=Te You can also use the TargetType AWS-provided key. For a list
// of valid resource type values that can be used with this key, see AWS resource
// and property types reference
// (http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-template-resource-type-ref.html)
// in the AWS CloudFormation User Guide. If you specify more than two keys, only
// documents that are identified by all the tags are returned in the results. If
// you specify more than two values for a key, documents that are identified by any
// of the values are returned in the results. To specify a custom key and value
// pair, use the format Key=tag:tagName,Values=valueName. For example, if you
// created a key called region and are using the AWS CLI to call the list-documents
// command: aws ssm list-documents --filters Key=tag:region,Values=east,west
// Key=Owner,Values=Self
type DocumentKeyValuesFilter struct {

	// The name of the filter key.
	Key *string

	// The value for the filter key.
	Values []string
}

// Details about the response to a document review request.
type DocumentMetadataResponseInfo struct {

	// Details about a reviewer's response to a document review request.
	ReviewerResponse []DocumentReviewerResponseSource
}

// Parameters specified in a System Manager document that run on the server when
// the command is run.
type DocumentParameter struct {

	// If specified, the default values for the parameters. Parameters without a
	// default value are required. Parameters with a default value are optional.
	DefaultValue *string

	// A description of what the parameter does, how to use it, the default value, and
	// whether or not the parameter is optional.
	Description *string

	// The name of the parameter.
	Name *string

	// The type of parameter. The type can be either String or StringList.
	Type DocumentParameterType
}

// An SSM document required by the current document.
type DocumentRequires struct {

	// The name of the required SSM document. The name can be an Amazon Resource Name
	// (ARN).
	//
	// This member is required.
	Name *string

	// The document version required by the current document.
	Version *string
}

// Information about comments added to a document review request.
type DocumentReviewCommentSource struct {

	// The content of a comment entered by a user who requests a review of a new
	// document version, or who reviews the new version.
	Content *string

	// The type of information added to a review request. Currently, only the value
	// Comment is supported.
	Type DocumentReviewCommentType
}

// Information about a reviewer's response to a document review request.
type DocumentReviewerResponseSource struct {

	// The comment entered by a reviewer as part of their document review response.
	Comment []DocumentReviewCommentSource

	// The date and time that a reviewer entered a response to a document review
	// request.
	CreateTime *time.Time

	// The current review status of a new custom SSM document created by a member of
	// your organization, or of the latest version of an existing SSM document. Only
	// one version of a document can be in the APPROVED state at a time. When a new
	// version is approved, the status of the previous version changes to REJECTED.
	// Only one version of a document can be in review, or PENDING, at a time.
	ReviewStatus ReviewStatus

	// The user in your organization assigned to review a document request.
	Reviewer *string

	// The date and time that a reviewer last updated a response to a document review
	// request.
	UpdatedTime *time.Time
}

// Information about a document approval review.
type DocumentReviews struct {

	// The action to take on a document approval review request.
	//
	// This member is required.
	Action DocumentReviewAction

	// A comment entered by a user in your organization about the document review
	// request.
	Comment []DocumentReviewCommentSource
}

// Version information about the document.
type DocumentVersionInfo struct {

	// The date the document was created.
	CreatedDate *time.Time

	// The document format, either JSON or YAML.
	DocumentFormat DocumentFormat

	// The document version.
	DocumentVersion *string

	// An identifier for the default version of the document.
	IsDefaultVersion bool

	// The document name.
	Name *string

	// The current status of the approval review for the latest version of the
	// document.
	ReviewStatus ReviewStatus

	// The status of the Systems Manager document, such as Creating, Active, Failed,
	// and Deleting.
	Status DocumentStatus

	// A message returned by AWS Systems Manager that explains the Status value. For
	// example, a Failed status might be explained by the StatusInformation message,
	// "The specified S3 bucket does not exist. Verify that the URL of the S3 bucket is
	// correct."
	StatusInformation *string

	// The version of the artifact associated with the document. For example, "Release
	// 12, Update 6". This value is unique across all versions of a document, and
	// cannot be changed.
	VersionName *string
}

// The EffectivePatch structure defines metadata about a patch along with the
// approval state of the patch in a particular patch baseline. The approval state
// includes information about whether the patch is currently approved, due to be
// approved by a rule, explicitly approved, or explicitly rejected and the date the
// patch was or will be approved.
type EffectivePatch struct {

	// Provides metadata for a patch, including information such as the KB ID,
	// severity, classification and a URL for where more information can be obtained
	// about the patch.
	Patch *Patch

	// The status of the patch in a patch baseline. This includes information about
	// whether the patch is currently approved, due to be approved by a rule,
	// explicitly approved, or explicitly rejected and the date the patch was or will
	// be approved.
	PatchStatus *PatchStatus
}

// Describes a failed association.
type FailedCreateAssociation struct {

	// The association.
	Entry *CreateAssociationBatchRequestEntry

	// The source of the failure.
	Fault Fault

	// A description of the failure.
	Message *string
}

// Information about an Automation failure.
type FailureDetails struct {

	// Detailed information about the Automation step failure.
	Details map[string][]string

	// The stage of the Automation execution when the failure occurred. The stages
	// include the following: InputValidation, PreVerification, Invocation,
	// PostVerification.
	FailureStage *string

	// The type of Automation failure. Failure types include the following: Action,
	// Permission, Throttling, Verification, Internal.
	FailureType *string
}

// Status information about the aggregated associations.
type InstanceAggregatedAssociationOverview struct {

	// Detailed status information about the aggregated associations.
	DetailedStatus *string

	// The number of associations for the instance(s).
	InstanceAssociationStatusAggregatedCount map[string]int32
}

// One or more association documents on the instance.
type InstanceAssociation struct {

	// The association ID.
	AssociationId *string

	// Version information for the association on the instance.
	AssociationVersion *string

	// The content of the association document for the instance(s).
	Content *string

	// The instance ID.
	InstanceId *string
}

// An S3 bucket where you want to store the results of this request. For the
// minimal permissions required to enable Amazon S3 output for an association, see
// Creating associations
// (https://docs.aws.amazon.com/systems-manager/latest/userguide/sysman-state-assoc.html)
// in the Systems Manager User Guide.
type InstanceAssociationOutputLocation struct {

	// An S3 bucket where you want to store the results of this request.
	S3Location *S3OutputLocation
}

// The URL of S3 bucket where you want to store the results of this request.
type InstanceAssociationOutputUrl struct {

	// The URL of S3 bucket where you want to store the results of this request.
	S3OutputUrl *S3OutputUrl
}

// Status information about the instance association.
type InstanceAssociationStatusInfo struct {

	// The association ID.
	AssociationId *string

	// The name of the association applied to the instance.
	AssociationName *string

	// The version of the association applied to the instance.
	AssociationVersion *string

	// Detailed status information about the instance association.
	DetailedStatus *string

	// The association document versions.
	DocumentVersion *string

	// An error code returned by the request to create the association.
	ErrorCode *string

	// The date the instance association ran.
	ExecutionDate *time.Time

	// Summary information about association execution.
	ExecutionSummary *string

	// The instance ID where the association was created.
	InstanceId *string

	// The name of the association.
	Name *string

	// A URL for an S3 bucket where you want to store the results of this request.
	OutputUrl *InstanceAssociationOutputUrl

	// Status information about the instance association.
	Status *string
}

// Describes a filter for a specific list of instances.
type InstanceInformation struct {

	// The activation ID created by Systems Manager when the server or VM was
	// registered.
	ActivationId *string

	// The version of SSM Agent running on your Linux instance.
	AgentVersion *string

	// Information about the association.
	AssociationOverview *InstanceAggregatedAssociationOverview

	// The status of the association.
	AssociationStatus *string

	// The fully qualified host name of the managed instance.
	ComputerName *string

	// The IP address of the managed instance.
	IPAddress *string

	// The Amazon Identity and Access Management (IAM) role assigned to the on-premises
	// Systems Manager managed instance. This call does not return the IAM role for EC2
	// instances. To retrieve the IAM role for an EC2 instance, use the Amazon EC2
	// DescribeInstances action. For information, see DescribeInstances
	// (http://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_DescribeInstances.html)
	// in the Amazon EC2 API Reference or describe-instances
	// (http://docs.aws.amazon.com/cli/latest/reference/ec2/describe-instances.html) in
	// the AWS CLI Command Reference.
	IamRole *string

	// The instance ID.
	InstanceId *string

	// Indicates whether the latest version of SSM Agent is running on your Linux
	// Managed Instance. This field does not indicate whether or not the latest version
	// is installed on Windows managed instances, because some older versions of
	// Windows Server use the EC2Config service to process SSM requests.
	IsLatestVersion bool

	// The date the association was last run.
	LastAssociationExecutionDate *time.Time

	// The date and time when the agent last pinged the Systems Manager service.
	LastPingDateTime *time.Time

	// The last date the association was successfully run.
	LastSuccessfulAssociationExecutionDate *time.Time

	// The name assigned to an on-premises server or virtual machine (VM) when it is
	// activated as a Systems Manager managed instance. The name is specified as the
	// DefaultInstanceName property using the CreateActivation command. It is applied
	// to the managed instance by specifying the Activation Code and Activation ID when
	// you install SSM Agent on the instance, as explained in Install SSM Agent for a
	// hybrid environment (Linux)
	// (http://docs.aws.amazon.com/systems-manager/latest/userguide/sysman-install-managed-linux.html)
	// and Install SSM Agent for a hybrid environment (Windows)
	// (http://docs.aws.amazon.com/systems-manager/latest/userguide/sysman-install-managed-win.html).
	// To retrieve the Name tag of an EC2 instance, use the Amazon EC2
	// DescribeInstances action. For information, see DescribeInstances
	// (http://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_DescribeInstances.html)
	// in the Amazon EC2 API Reference or describe-instances
	// (http://docs.aws.amazon.com/cli/latest/reference/ec2/describe-instances.html) in
	// the AWS CLI Command Reference.
	Name *string

	// Connection status of SSM Agent. The status Inactive has been deprecated and is
	// no longer in use.
	PingStatus PingStatus

	// The name of the operating system platform running on your instance.
	PlatformName *string

	// The operating system platform type.
	PlatformType PlatformType

	// The version of the OS platform running on your instance.
	PlatformVersion *string

	// The date the server or VM was registered with AWS as a managed instance.
	RegistrationDate *time.Time

	// The type of instance. Instances are either EC2 instances or managed instances.
	ResourceType ResourceType
}

// Describes a filter for a specific list of instances. You can filter instances
// information by using tags. You specify tags by using a key-value mapping. Use
// this action instead of the
// DescribeInstanceInformationRequest$InstanceInformationFilterList method. The
// InstanceInformationFilterList method is a legacy method and does not support
// tags.
type InstanceInformationFilter struct {

	// The name of the filter.
	//
	// This member is required.
	Key InstanceInformationFilterKey

	// The filter values.
	//
	// This member is required.
	ValueSet []string
}

// The filters to describe or get information about your managed instances.
type InstanceInformationStringFilter struct {

	// The filter key name to describe your instances. For example:
	// "InstanceIds"|"AgentVersion"|"PingStatus"|"PlatformTypes"|"ActivationIds"|"IamRole"|"ResourceType"|"AssociationStatus"|"Tag
	// Key" Tag key is not a valid filter. You must specify either tag-key or
	// tag:keyname and a string. Here are some valid examples: tag-key, tag:123,
	// tag:al!, tag:Windows. Here are some invalid examples: tag-keys, Tag Key, tag:,
	// tagKey, abc:keyname.
	//
	// This member is required.
	Key *string

	// The filter values.
	//
	// This member is required.
	Values []string
}

// Defines the high-level patch compliance state for a managed instance, providing
// information about the number of installed, missing, not applicable, and failed
// patches along with metadata about the operation when this information was
// gathered for the instance.
type InstancePatchState struct {

	// The ID of the patch baseline used to patch the instance.
	//
	// This member is required.
	BaselineId *string

	// The ID of the managed instance the high-level patch compliance information was
	// collected for.
	//
	// This member is required.
	InstanceId *string

	// The type of patching operation that was performed: SCAN (assess patch compliance
	// state) or INSTALL (install missing patches).
	//
	// This member is required.
	Operation PatchOperationType

	// The time the most recent patching operation completed on the instance.
	//
	// This member is required.
	OperationEndTime *time.Time

	// The time the most recent patching operation was started on the instance.
	//
	// This member is required.
	OperationStartTime *time.Time

	// The name of the patch group the managed instance belongs to.
	//
	// This member is required.
	PatchGroup *string

	// The number of patches from the patch baseline that were attempted to be
	// installed during the last patching operation, but failed to install.
	FailedCount int32

	// An https URL or an Amazon S3 path-style URL to a list of patches to be
	// installed. This patch installation list, which you maintain in an S3 bucket in
	// YAML format and specify in the SSM document AWS-RunPatchBaseline, overrides the
	// patches specified by the default patch baseline. For more information about the
	// InstallOverrideList parameter, see About the SSM document AWS-RunPatchBaseline
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/patch-manager-about-aws-runpatchbaseline.html)
	// in the AWS Systems Manager User Guide.
	InstallOverrideList *string

	// The number of patches from the patch baseline that are installed on the
	// instance.
	InstalledCount int32

	// The number of patches not specified in the patch baseline that are installed on
	// the instance.
	InstalledOtherCount int32

	// The number of patches installed by Patch Manager since the last time the
	// instance was rebooted.
	InstalledPendingRebootCount int32

	// The number of patches installed on an instance that are specified in a
	// RejectedPatches list. Patches with a status of InstalledRejected were typically
	// installed before they were added to a RejectedPatches list. If
	// ALLOW_AS_DEPENDENCY is the specified option for RejectedPatchesAction, the value
	// of InstalledRejectedCount will always be 0 (zero).
	InstalledRejectedCount int32

	// The time of the last attempt to patch the instance with NoReboot specified as
	// the reboot option.
	LastNoRebootInstallOperationTime *time.Time

	// The number of patches from the patch baseline that are applicable for the
	// instance but aren't currently installed.
	MissingCount int32

	// The number of patches from the patch baseline that aren't applicable for the
	// instance and therefore aren't installed on the instance. This number may be
	// truncated if the list of patch names is very large. The number of patches beyond
	// this limit are reported in UnreportedNotApplicableCount.
	NotApplicableCount int32

	// Placeholder information. This field will always be empty in the current release
	// of the service.
	OwnerInformation *string

	// Indicates the reboot option specified in the patch baseline. Reboot options
	// apply to Install operations only. Reboots are not attempted for Patch Manager
	// Scan operations.
	//
	// * RebootIfNeeded: Patch Manager tries to reboot the instance
	// if it installed any patches, or if any patches are detected with a status of
	// InstalledPendingReboot.
	//
	// * NoReboot: Patch Manager attempts to install missing
	// packages without trying to reboot the system. Patches installed with this option
	// are assigned a status of InstalledPendingReboot. These patches might not be in
	// effect until a reboot is performed.
	RebootOption RebootOption

	// The ID of the patch baseline snapshot used during the patching operation when
	// this compliance data was collected.
	SnapshotId *string

	// The number of patches beyond the supported limit of NotApplicableCount that are
	// not reported by name to Systems Manager Inventory.
	UnreportedNotApplicableCount int32
}

// Defines a filter used in DescribeInstancePatchStatesForPatchGroup used to scope
// down the information returned by the API.
type InstancePatchStateFilter struct {

	// The key for the filter. Supported values are FailedCount, InstalledCount,
	// InstalledOtherCount, MissingCount and NotApplicableCount.
	//
	// This member is required.
	Key *string

	// The type of comparison that should be performed for the value: Equal, NotEqual,
	// LessThan or GreaterThan.
	//
	// This member is required.
	Type InstancePatchStateOperatorType

	// The value for the filter, must be an integer greater than or equal to 0.
	//
	// This member is required.
	Values []string
}

// Specifies the inventory type and attribute for the aggregation execution.
type InventoryAggregator struct {

	// Nested aggregators to further refine aggregation for an inventory type.
	Aggregators []InventoryAggregator

	// The inventory type and attribute name for aggregation.
	Expression *string

	// A user-defined set of one or more filters on which to aggregate inventory data.
	// Groups return a count of resources that match and don't match the specified
	// criteria.
	Groups []InventoryGroup
}

// Status information returned by the DeleteInventory action.
type InventoryDeletionStatusItem struct {

	// The deletion ID returned by the DeleteInventory action.
	DeletionId *string

	// The UTC timestamp when the delete operation started.
	DeletionStartTime *time.Time

	// Information about the delete operation. For more information about this summary,
	// see Understanding the delete inventory summary
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/sysman-inventory-custom.html#sysman-inventory-delete)
	// in the AWS Systems Manager User Guide.
	DeletionSummary *InventoryDeletionSummary

	// The status of the operation. Possible values are InProgress and Complete.
	LastStatus InventoryDeletionStatus

	// Information about the status.
	LastStatusMessage *string

	// The UTC timestamp of when the last status report.
	LastStatusUpdateTime *time.Time

	// The name of the inventory data type.
	TypeName *string
}

// Information about the delete operation.
type InventoryDeletionSummary struct {

	// Remaining number of items to delete.
	RemainingCount int32

	// A list of counts and versions for deleted items.
	SummaryItems []InventoryDeletionSummaryItem

	// The total number of items to delete. This count does not change during the
	// delete operation.
	TotalCount int32
}

// Either a count, remaining count, or a version number in a delete inventory
// summary.
type InventoryDeletionSummaryItem struct {

	// A count of the number of deleted items.
	Count int32

	// The remaining number of items to delete.
	RemainingCount int32

	// The inventory type version.
	Version *string
}

// One or more filters. Use a filter to return a more specific list of results.
type InventoryFilter struct {

	// The name of the filter key.
	//
	// This member is required.
	Key *string

	// Inventory filter values. Example: inventory filter where instance IDs are
	// specified as values Key=AWS:InstanceInformation.InstanceId,Values=
	// i-a12b3c4d5e6g, i-1a2b3c4d5e6,Type=Equal
	//
	// This member is required.
	Values []string

	// The type of filter. The Exists filter must be used with aggregators. For more
	// information, see Aggregating inventory data
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/sysman-inventory-aggregate.html)
	// in the AWS Systems Manager User Guide.
	Type InventoryQueryOperatorType
}

// A user-defined set of one or more filters on which to aggregate inventory data.
// Groups return a count of resources that match and don't match the specified
// criteria.
type InventoryGroup struct {

	// Filters define the criteria for the group. The matchingCount field displays the
	// number of resources that match the criteria. The notMatchingCount field displays
	// the number of resources that don't match the criteria.
	//
	// This member is required.
	Filters []InventoryFilter

	// The name of the group.
	//
	// This member is required.
	Name *string
}

// Information collected from managed instances based on your inventory policy
// document
type InventoryItem struct {

	// The time the inventory information was collected.
	//
	// This member is required.
	CaptureTime *string

	// The schema version for the inventory item.
	//
	// This member is required.
	SchemaVersion *string

	// The name of the inventory type. Default inventory item type names start with
	// AWS. Custom inventory type names will start with Custom. Default inventory item
	// types include the following: AWS:AWSComponent, AWS:Application,
	// AWS:InstanceInformation, AWS:Network, and AWS:WindowsUpdate.
	//
	// This member is required.
	TypeName *string

	// The inventory data of the inventory type.
	Content []map[string]string

	// MD5 hash of the inventory item type contents. The content hash is used to
	// determine whether to update inventory information. The PutInventory API does not
	// update the inventory item type contents if the MD5 hash has not changed since
	// last update.
	ContentHash *string

	// A map of associated properties for a specified inventory type. For example, with
	// this attribute, you can specify the ExecutionId, ExecutionType, ComplianceType
	// properties of the AWS:ComplianceItem type.
	Context map[string]string
}

// Attributes are the entries within the inventory item content. It contains name
// and value.
type InventoryItemAttribute struct {

	// The data type of the inventory item attribute.
	//
	// This member is required.
	DataType InventoryAttributeDataType

	// Name of the inventory item attribute.
	//
	// This member is required.
	Name *string
}

// The inventory item schema definition. Users can use this to compose inventory
// query filters.
type InventoryItemSchema struct {

	// The schema attributes for inventory. This contains data type and attribute name.
	//
	// This member is required.
	Attributes []InventoryItemAttribute

	// The name of the inventory type. Default inventory item type names start with
	// AWS. Custom inventory type names will start with Custom. Default inventory item
	// types include the following: AWS:AWSComponent, AWS:Application,
	// AWS:InstanceInformation, AWS:Network, and AWS:WindowsUpdate.
	//
	// This member is required.
	TypeName *string

	// The alias name of the inventory type. The alias name is used for display
	// purposes.
	DisplayName *string

	// The schema version for the inventory item.
	Version *string
}

// Inventory query results.
type InventoryResultEntity struct {

	// The data section in the inventory result entity JSON.
	Data map[string]InventoryResultItem

	// ID of the inventory result entity. For example, for managed instance inventory
	// the result will be the managed instance ID. For EC2 instance inventory, the
	// result will be the instance ID.
	Id *string
}

// The inventory result item.
type InventoryResultItem struct {

	// Contains all the inventory data of the item type. Results include attribute
	// names and values.
	//
	// This member is required.
	Content []map[string]string

	// The schema version for the inventory result item/
	//
	// This member is required.
	SchemaVersion *string

	// The name of the inventory result item type.
	//
	// This member is required.
	TypeName *string

	// The time inventory item data was captured.
	CaptureTime *string

	// MD5 hash of the inventory item type contents. The content hash is used to
	// determine whether to update inventory information. The PutInventory API does not
	// update the inventory item type contents if the MD5 hash has not changed since
	// last update.
	ContentHash *string
}

// Information about an S3 bucket to write instance-level logs to. LoggingInfo has
// been deprecated. To specify an S3 bucket to contain logs, instead use the
// OutputS3BucketName and OutputS3KeyPrefix options in the TaskInvocationParameters
// structure. For information about how Systems Manager handles these options for
// the supported maintenance window task types, see
// MaintenanceWindowTaskInvocationParameters.
type LoggingInfo struct {

	// The name of an S3 bucket where execution logs are stored .
	//
	// This member is required.
	S3BucketName *string

	// The Region where the S3 bucket is located.
	//
	// This member is required.
	S3Region *string

	// (Optional) The S3 bucket subfolder.
	S3KeyPrefix *string
}

// The parameters for an AUTOMATION task type.
type MaintenanceWindowAutomationParameters struct {

	// The version of an Automation document to use during task execution.
	DocumentVersion *string

	// The parameters for the AUTOMATION task. For information about specifying and
	// updating task parameters, see RegisterTaskWithMaintenanceWindow and
	// UpdateMaintenanceWindowTask. LoggingInfo has been deprecated. To specify an S3
	// bucket to contain logs, instead use the OutputS3BucketName and OutputS3KeyPrefix
	// options in the TaskInvocationParameters structure. For information about how
	// Systems Manager handles these options for the supported maintenance window task
	// types, see MaintenanceWindowTaskInvocationParameters. TaskParameters has been
	// deprecated. To specify parameters to pass to a task when it runs, instead use
	// the Parameters option in the TaskInvocationParameters structure. For information
	// about how Systems Manager handles these options for the supported maintenance
	// window task types, see MaintenanceWindowTaskInvocationParameters. For AUTOMATION
	// task types, Systems Manager ignores any values specified for these parameters.
	Parameters map[string][]string
}

// Describes the information about an execution of a maintenance window.
type MaintenanceWindowExecution struct {

	// The time the execution finished.
	EndTime *time.Time

	// The time the execution started.
	StartTime *time.Time

	// The status of the execution.
	Status MaintenanceWindowExecutionStatus

	// The details explaining the Status. Only available for certain status values.
	StatusDetails *string

	// The ID of the maintenance window execution.
	WindowExecutionId *string

	// The ID of the maintenance window.
	WindowId *string
}

// Information about a task execution performed as part of a maintenance window
// execution.
type MaintenanceWindowExecutionTaskIdentity struct {

	// The time the task execution finished.
	EndTime *time.Time

	// The time the task execution started.
	StartTime *time.Time

	// The status of the task execution.
	Status MaintenanceWindowExecutionStatus

	// The details explaining the status of the task execution. Only available for
	// certain status values.
	StatusDetails *string

	// The ARN of the task that ran.
	TaskArn *string

	// The ID of the specific task execution in the maintenance window execution.
	TaskExecutionId *string

	// The type of task that ran.
	TaskType MaintenanceWindowTaskType

	// The ID of the maintenance window execution that ran the task.
	WindowExecutionId *string
}

// Describes the information about a task invocation for a particular target as
// part of a task execution performed as part of a maintenance window execution.
type MaintenanceWindowExecutionTaskInvocationIdentity struct {

	// The time the invocation finished.
	EndTime *time.Time

	// The ID of the action performed in the service that actually handled the task
	// invocation. If the task type is RUN_COMMAND, this value is the command ID.
	ExecutionId *string

	// The ID of the task invocation.
	InvocationId *string

	// User-provided value that was specified when the target was registered with the
	// maintenance window. This was also included in any CloudWatch events raised
	// during the task invocation.
	OwnerInformation *string

	// The parameters that were provided for the invocation when it was run.
	Parameters *string

	// The time the invocation started.
	StartTime *time.Time

	// The status of the task invocation.
	Status MaintenanceWindowExecutionStatus

	// The details explaining the status of the task invocation. Only available for
	// certain Status values.
	StatusDetails *string

	// The ID of the specific task execution in the maintenance window execution.
	TaskExecutionId *string

	// The task type.
	TaskType MaintenanceWindowTaskType

	// The ID of the maintenance window execution that ran the task.
	WindowExecutionId *string

	// The ID of the target definition in this maintenance window the invocation was
	// performed for.
	WindowTargetId *string
}

// Filter used in the request. Supported filter keys are Name and Enabled.
type MaintenanceWindowFilter struct {

	// The name of the filter.
	Key *string

	// The filter values.
	Values []string
}

// Information about the maintenance window.
type MaintenanceWindowIdentity struct {

	// The number of hours before the end of the maintenance window that Systems
	// Manager stops scheduling new tasks for execution.
	Cutoff int32

	// A description of the maintenance window.
	Description *string

	// The duration of the maintenance window in hours.
	Duration int32

	// Indicates whether the maintenance window is enabled.
	Enabled bool

	// The date and time, in ISO-8601 Extended format, for when the maintenance window
	// is scheduled to become inactive.
	EndDate *string

	// The name of the maintenance window.
	Name *string

	// The next time the maintenance window will actually run, taking into account any
	// specified times for the maintenance window to become active or inactive.
	NextExecutionTime *string

	// The schedule of the maintenance window in the form of a cron or rate expression.
	Schedule *string

	// The number of days to wait to run a maintenance window after the scheduled CRON
	// expression date and time.
	ScheduleOffset int32

	// The time zone that the scheduled maintenance window executions are based on, in
	// Internet Assigned Numbers Authority (IANA) format.
	ScheduleTimezone *string

	// The date and time, in ISO-8601 Extended format, for when the maintenance window
	// is scheduled to become active.
	StartDate *string

	// The ID of the maintenance window.
	WindowId *string
}

// The maintenance window to which the specified target belongs.
type MaintenanceWindowIdentityForTarget struct {

	// The name of the maintenance window.
	Name *string

	// The ID of the maintenance window.
	WindowId *string
}

// The parameters for a LAMBDA task type. For information about specifying and
// updating task parameters, see RegisterTaskWithMaintenanceWindow and
// UpdateMaintenanceWindowTask. LoggingInfo has been deprecated. To specify an S3
// bucket to contain logs, instead use the OutputS3BucketName and OutputS3KeyPrefix
// options in the TaskInvocationParameters structure. For information about how
// Systems Manager handles these options for the supported maintenance window task
// types, see MaintenanceWindowTaskInvocationParameters. TaskParameters has been
// deprecated. To specify parameters to pass to a task when it runs, instead use
// the Parameters option in the TaskInvocationParameters structure. For information
// about how Systems Manager handles these options for the supported maintenance
// window task types, see MaintenanceWindowTaskInvocationParameters. For Lambda
// tasks, Systems Manager ignores any values specified for TaskParameters and
// LoggingInfo.
type MaintenanceWindowLambdaParameters struct {

	// Pass client-specific information to the Lambda function that you are invoking.
	// You can then process the client information in your Lambda function as you
	// choose through the context variable.
	ClientContext *string

	// JSON to provide to your Lambda function as input.
	Payload []byte

	// (Optional) Specify a Lambda function version or alias name. If you specify a
	// function version, the action uses the qualified function ARN to invoke a
	// specific Lambda function. If you specify an alias name, the action uses the
	// alias ARN to invoke the Lambda function version to which the alias points.
	Qualifier *string
}

// The parameters for a RUN_COMMAND task type. For information about specifying and
// updating task parameters, see RegisterTaskWithMaintenanceWindow and
// UpdateMaintenanceWindowTask. LoggingInfo has been deprecated. To specify an S3
// bucket to contain logs, instead use the OutputS3BucketName and OutputS3KeyPrefix
// options in the TaskInvocationParameters structure. For information about how
// Systems Manager handles these options for the supported maintenance window task
// types, see MaintenanceWindowTaskInvocationParameters. TaskParameters has been
// deprecated. To specify parameters to pass to a task when it runs, instead use
// the Parameters option in the TaskInvocationParameters structure. For information
// about how Systems Manager handles these options for the supported maintenance
// window task types, see MaintenanceWindowTaskInvocationParameters. For Run
// Command tasks, Systems Manager uses specified values for TaskParameters and
// LoggingInfo only if no values are specified for TaskInvocationParameters.
type MaintenanceWindowRunCommandParameters struct {

	// Configuration options for sending command output to CloudWatch Logs.
	CloudWatchOutputConfig *CloudWatchOutputConfig

	// Information about the commands to run.
	Comment *string

	// The SHA-256 or SHA-1 hash created by the system when the document was created.
	// SHA-1 hashes have been deprecated.
	DocumentHash *string

	// SHA-256 or SHA-1. SHA-1 hashes have been deprecated.
	DocumentHashType DocumentHashType

	// The SSM document version to use in the request. You can specify $DEFAULT,
	// $LATEST, or a specific version number. If you run commands by using the AWS CLI,
	// then you must escape the first two options by using a backslash. If you specify
	// a version number, then you don't need to use the backslash. For example:
	// --document-version "\$DEFAULT" --document-version "\$LATEST" --document-version
	// "3"
	DocumentVersion *string

	// Configurations for sending notifications about command status changes on a
	// per-instance basis.
	NotificationConfig *NotificationConfig

	// The name of the S3 bucket.
	OutputS3BucketName *string

	// The S3 bucket subfolder.
	OutputS3KeyPrefix *string

	// The parameters for the RUN_COMMAND task execution.
	Parameters map[string][]string

	// The ARN of the IAM service role to use to publish Amazon Simple Notification
	// Service (Amazon SNS) notifications for maintenance window Run Command tasks.
	ServiceRoleArn *string

	// If this time is reached and the command has not already started running, it
	// doesn't run.
	TimeoutSeconds int32
}

// The parameters for a STEP_FUNCTIONS task. For information about specifying and
// updating task parameters, see RegisterTaskWithMaintenanceWindow and
// UpdateMaintenanceWindowTask. LoggingInfo has been deprecated. To specify an S3
// bucket to contain logs, instead use the OutputS3BucketName and OutputS3KeyPrefix
// options in the TaskInvocationParameters structure. For information about how
// Systems Manager handles these options for the supported maintenance window task
// types, see MaintenanceWindowTaskInvocationParameters. TaskParameters has been
// deprecated. To specify parameters to pass to a task when it runs, instead use
// the Parameters option in the TaskInvocationParameters structure. For information
// about how Systems Manager handles these options for the supported maintenance
// window task types, see MaintenanceWindowTaskInvocationParameters. For Step
// Functions tasks, Systems Manager ignores any values specified for TaskParameters
// and LoggingInfo.
type MaintenanceWindowStepFunctionsParameters struct {

	// The inputs for the STEP_FUNCTIONS task.
	Input *string

	// The name of the STEP_FUNCTIONS task.
	Name *string
}

// The target registered with the maintenance window.
type MaintenanceWindowTarget struct {

	// A description for the target.
	Description *string

	// The name for the maintenance window target.
	Name *string

	// A user-provided value that will be included in any CloudWatch events that are
	// raised while running tasks for these targets in this maintenance window.
	OwnerInformation *string

	// The type of target that is being registered with the maintenance window.
	ResourceType MaintenanceWindowResourceType

	// The targets, either instances or tags. Specify instances using the following
	// format: Key=instanceids,Values=, Tags are specified using the following format:
	// Key=,Values=.
	Targets []Target

	// The ID of the maintenance window to register the target with.
	WindowId *string

	// The ID of the target.
	WindowTargetId *string
}

// Information about a task defined for a maintenance window.
type MaintenanceWindowTask struct {

	// A description of the task.
	Description *string

	// Information about an S3 bucket to write task-level logs to. LoggingInfo has been
	// deprecated. To specify an S3 bucket to contain logs, instead use the
	// OutputS3BucketName and OutputS3KeyPrefix options in the TaskInvocationParameters
	// structure. For information about how Systems Manager handles these options for
	// the supported maintenance window task types, see
	// MaintenanceWindowTaskInvocationParameters.
	LoggingInfo *LoggingInfo

	// The maximum number of targets this task can be run for, in parallel.
	MaxConcurrency *string

	// The maximum number of errors allowed before this task stops being scheduled.
	MaxErrors *string

	// The task name.
	Name *string

	// The priority of the task in the maintenance window. The lower the number, the
	// higher the priority. Tasks that have the same priority are scheduled in
	// parallel.
	Priority int32

	// The ARN of the IAM service role to use to publish Amazon Simple Notification
	// Service (Amazon SNS) notifications for maintenance window Run Command tasks.
	ServiceRoleArn *string

	// The targets (either instances or tags). Instances are specified using
	// Key=instanceids,Values=,. Tags are specified using Key=,Values=.
	Targets []Target

	// The resource that the task uses during execution. For RUN_COMMAND and AUTOMATION
	// task types, TaskArn is the Systems Manager document name or ARN. For LAMBDA
	// tasks, it's the function name or ARN. For STEP_FUNCTIONS tasks, it's the state
	// machine ARN.
	TaskArn *string

	// The parameters that should be passed to the task when it is run. TaskParameters
	// has been deprecated. To specify parameters to pass to a task when it runs,
	// instead use the Parameters option in the TaskInvocationParameters structure. For
	// information about how Systems Manager handles these options for the supported
	// maintenance window task types, see MaintenanceWindowTaskInvocationParameters.
	TaskParameters map[string]MaintenanceWindowTaskParameterValueExpression

	// The type of task. The type can be one of the following: RUN_COMMAND, AUTOMATION,
	// LAMBDA, or STEP_FUNCTIONS.
	Type MaintenanceWindowTaskType

	// The ID of the maintenance window where the task is registered.
	WindowId *string

	// The task ID.
	WindowTaskId *string
}

// The parameters for task execution.
type MaintenanceWindowTaskInvocationParameters struct {

	// The parameters for an AUTOMATION task type.
	Automation *MaintenanceWindowAutomationParameters

	// The parameters for a LAMBDA task type.
	Lambda *MaintenanceWindowLambdaParameters

	// The parameters for a RUN_COMMAND task type.
	RunCommand *MaintenanceWindowRunCommandParameters

	// The parameters for a STEP_FUNCTIONS task type.
	StepFunctions *MaintenanceWindowStepFunctionsParameters
}

// Defines the values for a task parameter.
type MaintenanceWindowTaskParameterValueExpression struct {

	// This field contains an array of 0 or more strings, each 1 to 255 characters in
	// length.
	Values []string
}

// Metadata to assign to an Application Manager application.
type MetadataValue struct {

	// Metadata value to assign to an Application Manager application.
	Value *string
}

// A summary of resources that are not compliant. The summary is organized
// according to resource type.
type NonCompliantSummary struct {

	// The total number of compliance items that are not compliant.
	NonCompliantCount int32

	// A summary of the non-compliance severity by compliance type
	SeveritySummary *SeveritySummary
}

// Configurations for sending notifications.
type NotificationConfig struct {

	// An Amazon Resource Name (ARN) for an Amazon Simple Notification Service (Amazon
	// SNS) topic. Run Command pushes notifications about command status changes to
	// this topic.
	NotificationArn *string

	// The different events for which you can receive notifications. These events
	// include the following: All (events), InProgress, Success, TimedOut, Cancelled,
	// Failed. To learn more about these events, see Monitoring Systems Manager status
	// changes using Amazon SNS notifications
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/monitoring-sns-notifications.html)
	// in the AWS Systems Manager User Guide.
	NotificationEvents []NotificationEvent

	// Command: Receive notification when the status of a command changes. Invocation:
	// For commands sent to multiple instances, receive notification on a per-instance
	// basis when the status of a command changes.
	NotificationType NotificationType
}

// One or more aggregators for viewing counts of OpsItems using different
// dimensions such as Source, CreatedTime, or Source and CreatedTime, to name a
// few.
type OpsAggregator struct {

	// Either a Range or Count aggregator for limiting an OpsItem summary.
	AggregatorType *string

	// A nested aggregator for viewing counts of OpsItems.
	Aggregators []OpsAggregator

	// The name of an OpsItem attribute on which to limit the count of OpsItems.
	AttributeName *string

	// The aggregator filters.
	Filters []OpsFilter

	// The data type name to use for viewing counts of OpsItems.
	TypeName *string

	// The aggregator value.
	Values map[string]string
}

// The result of the query.
type OpsEntity struct {

	// The data returned by the query.
	Data map[string]OpsEntityItem

	// The query ID.
	Id *string
}

// The OpsItem summaries result item.
type OpsEntityItem struct {

	// The time OpsItem data was captured.
	CaptureTime *string

	// The detailed data content for an OpsItem summaries result item.
	Content []map[string]string
}

// A filter for viewing OpsItem summaries.
type OpsFilter struct {

	// The name of the filter.
	//
	// This member is required.
	Key *string

	// The filter value.
	//
	// This member is required.
	Values []string

	// The type of filter.
	Type OpsFilterOperatorType
}

// Operations engineers and IT professionals use OpsCenter to view, investigate,
// and remediate operational issues impacting the performance and health of their
// AWS resources. For more information, see AWS Systems Manager OpsCenter
// (https://docs.aws.amazon.com/systems-manager/latest/userguide/OpsCenter.html) in
// the AWS Systems Manager User Guide.
type OpsItem struct {

	// The time a runbook workflow ended. Currently reported only for the OpsItem type
	// /aws/changerequest.
	ActualEndTime *time.Time

	// The time a runbook workflow started. Currently reported only for the OpsItem
	// type /aws/changerequest.
	ActualStartTime *time.Time

	// An OpsItem category. Category options include: Availability, Cost, Performance,
	// Recovery, Security.
	Category *string

	// The ARN of the AWS account that created the OpsItem.
	CreatedBy *string

	// The date and time the OpsItem was created.
	CreatedTime *time.Time

	// The OpsItem description.
	Description *string

	// The ARN of the AWS account that last updated the OpsItem.
	LastModifiedBy *string

	// The date and time the OpsItem was last updated.
	LastModifiedTime *time.Time

	// The Amazon Resource Name (ARN) of an SNS topic where notifications are sent when
	// this OpsItem is edited or changed.
	Notifications []OpsItemNotification

	// Operational data is custom data that provides useful reference details about the
	// OpsItem. For example, you can specify log files, error strings, license keys,
	// troubleshooting tips, or other relevant data. You enter operational data as
	// key-value pairs. The key has a maximum length of 128 characters. The value has a
	// maximum size of 20 KB. Operational data keys can't begin with the following:
	// amazon, aws, amzn, ssm, /amazon, /aws, /amzn, /ssm. You can choose to make the
	// data searchable by other users in the account or you can restrict search access.
	// Searchable data means that all users with access to the OpsItem Overview page
	// (as provided by the DescribeOpsItems API action) can view and search on the
	// specified data. Operational data that is not searchable is only viewable by
	// users who have access to the OpsItem (as provided by the GetOpsItem API action).
	// Use the /aws/resources key in OperationalData to specify a related resource in
	// the request. Use the /aws/automations key in OperationalData to associate an
	// Automation runbook with the OpsItem. To view AWS CLI example commands that use
	// these keys, see Creating OpsItems manually
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/OpsCenter-creating-OpsItems.html#OpsCenter-manually-create-OpsItems)
	// in the AWS Systems Manager User Guide.
	OperationalData map[string]OpsItemDataValue

	// The ID of the OpsItem.
	OpsItemId *string

	// The type of OpsItem. Currently, the only valid values are /aws/changerequest and
	// /aws/issue.
	OpsItemType *string

	// The time specified in a change request for a runbook workflow to end. Currently
	// supported only for the OpsItem type /aws/changerequest.
	PlannedEndTime *time.Time

	// The time specified in a change request for a runbook workflow to start.
	// Currently supported only for the OpsItem type /aws/changerequest.
	PlannedStartTime *time.Time

	// The importance of this OpsItem in relation to other OpsItems in the system.
	Priority *int32

	// One or more OpsItems that share something in common with the current OpsItem.
	// For example, related OpsItems can include OpsItems with similar error messages,
	// impacted resources, or statuses for the impacted resource.
	RelatedOpsItems []RelatedOpsItem

	// The severity of the OpsItem. Severity options range from 1 to 4.
	Severity *string

	// The origin of the OpsItem, such as Amazon EC2 or Systems Manager. The impacted
	// resource is a subset of source.
	Source *string

	// The OpsItem status. Status can be Open, In Progress, or Resolved. For more
	// information, see Editing OpsItem details
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/OpsCenter-working-with-OpsItems-editing-details.html)
	// in the AWS Systems Manager User Guide.
	Status OpsItemStatus

	// A short heading that describes the nature of the OpsItem and the impacted
	// resource.
	Title *string

	// The version of this OpsItem. Each time the OpsItem is edited the version number
	// increments by one.
	Version *string
}

// An object that defines the value of the key and its type in the OperationalData
// map.
type OpsItemDataValue struct {

	// The type of key-value pair. Valid types include SearchableString and String.
	Type OpsItemDataType

	// The value of the OperationalData key.
	Value *string
}

// Describes a filter for a specific list of OpsItem events. You can filter event
// information by using tags. You specify tags by using a key-value pair mapping.
type OpsItemEventFilter struct {

	// The name of the filter key. Currently, the only supported value is OpsItemId.
	//
	// This member is required.
	Key OpsItemEventFilterKey

	// The operator used by the filter call. Currently, the only supported value is
	// Equal.
	//
	// This member is required.
	Operator OpsItemEventFilterOperator

	// The values for the filter, consisting of one or more OpsItem IDs.
	//
	// This member is required.
	Values []string
}

// Summary information about an OpsItem event.
type OpsItemEventSummary struct {

	// Information about the user or resource that created the OpsItem event.
	CreatedBy *OpsItemIdentity

	// The date and time the OpsItem event was created.
	CreatedTime *time.Time

	// Specific information about the OpsItem event.
	Detail *string

	// The type of information provided as a detail.
	DetailType *string

	// The ID of the OpsItem event.
	EventId *string

	// The ID of the OpsItem.
	OpsItemId *string

	// The source of the OpsItem event.
	Source *string
}

// Describes an OpsItem filter.
type OpsItemFilter struct {

	// The name of the filter.
	//
	// This member is required.
	Key OpsItemFilterKey

	// The operator used by the filter call.
	//
	// This member is required.
	Operator OpsItemFilterOperator

	// The filter value.
	//
	// This member is required.
	Values []string
}

// Information about the user or resource that created an OpsItem event.
type OpsItemIdentity struct {

	// The Amazon Resource Name (ARN) of the IAM entity that created the OpsItem event.
	Arn *string
}

// A notification about the OpsItem.
type OpsItemNotification struct {

	// The Amazon Resource Name (ARN) of an SNS topic where notifications are sent when
	// this OpsItem is edited or changed.
	Arn *string
}

// A count of OpsItems.
type OpsItemSummary struct {

	// The time a runbook workflow ended. Currently reported only for the OpsItem type
	// /aws/changerequest.
	ActualEndTime *time.Time

	// The time a runbook workflow started. Currently reported only for the OpsItem
	// type /aws/changerequest.
	ActualStartTime *time.Time

	// A list of OpsItems by category.
	Category *string

	// The Amazon Resource Name (ARN) of the IAM entity that created the OpsItem.
	CreatedBy *string

	// The date and time the OpsItem was created.
	CreatedTime *time.Time

	// The Amazon Resource Name (ARN) of the IAM entity that created the OpsItem.
	LastModifiedBy *string

	// The date and time the OpsItem was last updated.
	LastModifiedTime *time.Time

	// Operational data is custom data that provides useful reference details about the
	// OpsItem.
	OperationalData map[string]OpsItemDataValue

	// The ID of the OpsItem.
	OpsItemId *string

	// The type of OpsItem. Currently, the only valid values are /aws/changerequest and
	// /aws/issue.
	OpsItemType *string

	// The time specified in a change request for a runbook workflow to end. Currently
	// supported only for the OpsItem type /aws/changerequest.
	PlannedEndTime *time.Time

	// The time specified in a change request for a runbook workflow to start.
	// Currently supported only for the OpsItem type /aws/changerequest.
	PlannedStartTime *time.Time

	// The importance of this OpsItem in relation to other OpsItems in the system.
	Priority *int32

	// A list of OpsItems by severity.
	Severity *string

	// The impacted AWS resource.
	Source *string

	// The OpsItem status. Status can be Open, In Progress, or Resolved.
	Status OpsItemStatus

	// A short heading that describes the nature of the OpsItem and the impacted
	// resource.
	Title *string
}

// Operational metadata for an application in Application Manager.
type OpsMetadata struct {

	// The date the OpsMetadata objects was created.
	CreationDate *time.Time

	// The date the OpsMetadata object was last updated.
	LastModifiedDate *time.Time

	// The user name who last updated the OpsMetadata object.
	LastModifiedUser *string

	// The Amazon Resource Name (ARN) of the OpsMetadata Object or blob.
	OpsMetadataArn *string

	// The ID of the Application Manager application.
	ResourceId *string
}

// A filter to limit the number of OpsMetadata objects displayed.
type OpsMetadataFilter struct {

	// A filter key.
	//
	// This member is required.
	Key *string

	// A filter value.
	//
	// This member is required.
	Values []string
}

// The OpsItem data type to return.
type OpsResultAttribute struct {

	// Name of the data type. Valid value: AWS:OpsItem, AWS:EC2InstanceInformation,
	// AWS:OpsItemTrendline, or AWS:ComplianceSummary.
	//
	// This member is required.
	TypeName *string
}

// Information about the source where the association execution details are stored.
type OutputSource struct {

	// The ID of the output source, for example the URL of an S3 bucket.
	OutputSourceId *string

	// The type of source where the association execution details are stored, for
	// example, Amazon S3.
	OutputSourceType *string
}

// An Systems Manager parameter in Parameter Store.
type Parameter struct {

	// The Amazon Resource Name (ARN) of the parameter.
	ARN *string

	// The data type of the parameter, such as text or aws:ec2:image. The default is
	// text.
	DataType *string

	// Date the parameter was last changed or updated and the parameter version was
	// created.
	LastModifiedDate *time.Time

	// The name of the parameter.
	Name *string

	// Either the version number or the label used to retrieve the parameter value.
	// Specify selectors by using one of the following formats: parameter_name:version
	// parameter_name:label
	Selector *string

	// Applies to parameters that reference information in other AWS services.
	// SourceResult is the raw result or response from the source.
	SourceResult *string

	// The type of parameter. Valid values include the following: String, StringList,
	// and SecureString.
	Type ParameterType

	// The parameter value.
	Value *string

	// The parameter version.
	Version int64
}

// Information about parameter usage.
type ParameterHistory struct {

	// Parameter names can include the following letters and symbols. a-zA-Z0-9_.-
	AllowedPattern *string

	// The data type of the parameter, such as text or aws:ec2:image. The default is
	// text.
	DataType *string

	// Information about the parameter.
	Description *string

	// The ID of the query key used for this parameter.
	KeyId *string

	// Labels assigned to the parameter version.
	Labels []string

	// Date the parameter was last changed or updated.
	LastModifiedDate *time.Time

	// Amazon Resource Name (ARN) of the AWS user who last changed the parameter.
	LastModifiedUser *string

	// The name of the parameter.
	Name *string

	// Information about the policies assigned to a parameter. Assigning parameter
	// policies
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/parameter-store-policies.html)
	// in the AWS Systems Manager User Guide.
	Policies []ParameterInlinePolicy

	// The parameter tier.
	Tier ParameterTier

	// The type of parameter used.
	Type ParameterType

	// The parameter value.
	Value *string

	// The parameter version.
	Version int64
}

// One or more policies assigned to a parameter.
type ParameterInlinePolicy struct {

	// The status of the policy. Policies report the following statuses: Pending (the
	// policy has not been enforced or applied yet), Finished (the policy was applied),
	// Failed (the policy was not applied), or InProgress (the policy is being applied
	// now).
	PolicyStatus *string

	// The JSON text of the policy.
	PolicyText *string

	// The type of policy. Parameter Store supports the following policy types:
	// Expiration, ExpirationNotification, and NoChangeNotification.
	PolicyType *string
}

// Metadata includes information like the ARN of the last user and the date/time
// the parameter was last used.
type ParameterMetadata struct {

	// A parameter name can include only the following letters and symbols.
	// a-zA-Z0-9_.-
	AllowedPattern *string

	// The data type of the parameter, such as text or aws:ec2:image. The default is
	// text.
	DataType *string

	// Description of the parameter actions.
	Description *string

	// The ID of the query key used for this parameter.
	KeyId *string

	// Date the parameter was last changed or updated.
	LastModifiedDate *time.Time

	// Amazon Resource Name (ARN) of the AWS user who last changed the parameter.
	LastModifiedUser *string

	// The parameter name.
	Name *string

	// A list of policies associated with a parameter.
	Policies []ParameterInlinePolicy

	// The parameter tier.
	Tier ParameterTier

	// The type of parameter. Valid parameter types include the following: String,
	// StringList, and SecureString.
	Type ParameterType

	// The parameter version.
	Version int64
}

// This data type is deprecated. Instead, use ParameterStringFilter.
type ParametersFilter struct {

	// The name of the filter.
	//
	// This member is required.
	Key ParametersFilterKey

	// The filter values.
	//
	// This member is required.
	Values []string
}

// One or more filters. Use a filter to return a more specific list of results.
type ParameterStringFilter struct {

	// The name of the filter. The ParameterStringFilter object is used by the
	// DescribeParameters and GetParametersByPath API actions. However, not all of the
	// pattern values listed for Key can be used with both actions. For
	// DescribeActions, all of the listed patterns are valid, with the exception of
	// Label. For GetParametersByPath, the following patterns listed for Key are not
	// valid: tag, Name, Path, and Tier. For examples of CLI commands demonstrating
	// valid parameter filter constructions, see Searching for Systems Manager
	// parameters
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/parameter-search.html)
	// in the AWS Systems Manager User Guide.
	//
	// This member is required.
	Key *string

	// For all filters used with DescribeParameters, valid options include Equals and
	// BeginsWith. The Name filter additionally supports the Contains option.
	// (Exception: For filters using the key Path, valid options include Recursive and
	// OneLevel.) For filters used with GetParametersByPath, valid options include
	// Equals and BeginsWith. (Exception: For filters using Label as the Key name, the
	// only valid option is Equals.)
	Option *string

	// The value you want to search for.
	Values []string
}

// Represents metadata about a patch.
type Patch struct {

	// The Advisory ID of the patch. For example, RHSA-2020:3779. Applies to
	// Linux-based instances only.
	AdvisoryIds []string

	// The architecture of the patch. For example, in
	// example-pkg-0.710.10-2.7.abcd.x86_64, the architecture is indicated by x86_64.
	// Applies to Linux-based instances only.
	Arch *string

	// The Bugzilla ID of the patch. For example, 1600646. Applies to Linux-based
	// instances only.
	BugzillaIds []string

	// The Common Vulnerabilities and Exposures (CVE) ID of the patch. For example,
	// CVE-2011-3192. Applies to Linux-based instances only.
	CVEIds []string

	// The classification of the patch. For example, SecurityUpdates, Updates, or
	// CriticalUpdates.
	Classification *string

	// The URL where more information can be obtained about the patch.
	ContentUrl *string

	// The description of the patch.
	Description *string

	// The epoch of the patch. For example in pkg-example-EE-20180914-2.2.amzn1.noarch,
	// the epoch value is 20180914-2. Applies to Linux-based instances only.
	Epoch int32

	// The ID of the patch. Applies to Windows patches only. This ID is not the same as
	// the Microsoft Knowledge Base ID.
	Id *string

	// The Microsoft Knowledge Base ID of the patch. Applies to Windows patches only.
	KbNumber *string

	// The language of the patch if it's language-specific.
	Language *string

	// The ID of the Microsoft Security Response Center (MSRC) bulletin the patch is
	// related to. For example, MS14-045. Applies to Windows patches only.
	MsrcNumber *string

	// The severity of the patch, such as Critical, Important, or Moderate. Applies to
	// Windows patches only.
	MsrcSeverity *string

	// The name of the patch. Applies to Linux-based instances only.
	Name *string

	// The specific product the patch is applicable for. For example, WindowsServer2016
	// or AmazonLinux2018.03.
	Product *string

	// The product family the patch is applicable for. For example, Windows or Amazon
	// Linux 2.
	ProductFamily *string

	// The particular release of a patch. For example, in
	// pkg-example-EE-20180914-2.2.amzn1.noarch, the release is 2.amaz1. Applies to
	// Linux-based instances only.
	Release *string

	// The date the patch was released.
	ReleaseDate *time.Time

	// The source patch repository for the operating system and version, such as
	// trusty-security for Ubuntu Server 14.04 LTE and focal-security for Ubuntu Server
	// 20.04 LTE. Applies to Linux-based instances only.
	Repository *string

	// The severity level of the patch. For example, CRITICAL or MODERATE.
	Severity *string

	// The title of the patch.
	Title *string

	// The name of the vendor providing the patch.
	Vendor *string

	// The version number of the patch. For example, in
	// example-pkg-1.710.10-2.7.abcd.x86_64, the version number is indicated by -1.
	// Applies to Linux-based instances only.
	Version *string
}

// Defines the basic information about a patch baseline.
type PatchBaselineIdentity struct {

	// The description of the patch baseline.
	BaselineDescription *string

	// The ID of the patch baseline.
	BaselineId *string

	// The name of the patch baseline.
	BaselineName *string

	// Whether this is the default baseline. Note that Systems Manager supports
	// creating multiple default patch baselines. For example, you can create a default
	// patch baseline for each operating system.
	DefaultBaseline bool

	// Defines the operating system the patch baseline applies to. The Default value is
	// WINDOWS.
	OperatingSystem OperatingSystem
}

// Information about the state of a patch on a particular instance as it relates to
// the patch baseline used to patch the instance.
type PatchComplianceData struct {

	// The classification of the patch (for example, SecurityUpdates, Updates,
	// CriticalUpdates).
	//
	// This member is required.
	Classification *string

	// The date/time the patch was installed on the instance. Note that not all
	// operating systems provide this level of information.
	//
	// This member is required.
	InstalledTime *time.Time

	// The operating system-specific ID of the patch.
	//
	// This member is required.
	KBId *string

	// The severity of the patch (for example, Critical, Important, Moderate).
	//
	// This member is required.
	Severity *string

	// The state of the patch on the instance, such as INSTALLED or FAILED. For
	// descriptions of each patch state, see About patch compliance
	// (https://docs.aws.amazon.com/systems-manager/latest/userguide/sysman-compliance-about.html#sysman-compliance-monitor-patch)
	// in the AWS Systems Manager User Guide.
	//
	// This member is required.
	State PatchComplianceDataState

	// The title of the patch.
	//
	// This member is required.
	Title *string

	// The IDs of one or more Common Vulnerabilities and Exposure (CVE) issues that are
	// resolved by the patch.
	CVEIds *string
}

// Defines which patches should be included in a patch baseline. A patch filter
// consists of a key and a set of values. The filter key is a patch property. For
// example, the available filter keys for WINDOWS are PATCH_SET, PRODUCT,
// PRODUCT_FAMILY, CLASSIFICATION, and MSRC_SEVERITY. The filter values define a
// matching criterion for the patch property indicated by the key. For example, if
// the filter key is PRODUCT and the filter values are ["Office 2013", "Office
// 2016"], then the filter accepts all patches where product name is either "Office
// 2013" or "Office 2016". The filter values can be exact values for the patch
// property given as a key, or a wildcard (*), which matches all values. You can
// view lists of valid values for the patch properties by running the
// DescribePatchProperties command. For information about which patch properties
// can be used with each major operating system, see DescribePatchProperties.
type PatchFilter struct {

	// The key for the filter. Run the DescribePatchProperties command to view lists of
	// valid keys for each operating system type.
	//
	// This member is required.
	Key PatchFilterKey

	// The value for the filter key. Run the DescribePatchProperties command to view
	// lists of valid values for each key based on operating system type.
	//
	// This member is required.
	Values []string
}

// A set of patch filters, typically used for approval rules.
type PatchFilterGroup struct {

	// The set of patch filters that make up the group.
	//
	// This member is required.
	PatchFilters []PatchFilter
}

// The mapping between a patch group and the patch baseline the patch group is
// registered with.
type PatchGroupPatchBaselineMapping struct {

	// The patch baseline the patch group is registered with.
	BaselineIdentity *PatchBaselineIdentity

	// The name of the patch group registered with the patch baseline.
	PatchGroup *string
}

// Defines a filter used in Patch Manager APIs.
type PatchOrchestratorFilter struct {

	// The key for the filter.
	Key *string

	// The value for the filter.
	Values []string
}

// Defines an approval rule for a patch baseline.
type PatchRule struct {

	// The patch filter group that defines the criteria for the rule.
	//
	// This member is required.
	PatchFilterGroup *PatchFilterGroup

	// The number of days after the release date of each patch matched by the rule that
	// the patch is marked as approved in the patch baseline. For example, a value of 7
	// means that patches are approved seven days after they are released. Not
	// supported on Debian Server or Ubuntu Server.
	ApproveAfterDays int32

	// The cutoff date for auto approval of released patches. Any patches released on
	// or before this date are installed automatically. Not supported on Debian Server
	// or Ubuntu Server. Enter dates in the format YYYY-MM-DD. For example, 2020-12-31.
	ApproveUntilDate *string

	// A compliance severity level for all approved patches in a patch baseline.
	ComplianceLevel PatchComplianceLevel

	// For instances identified by the approval rule filters, enables a patch baseline
	// to apply non-security updates available in the specified repository. The default
	// value is 'false'. Applies to Linux instances only.
	EnableNonSecurity bool
}

// A set of rules defining the approval rules for a patch baseline.
type PatchRuleGroup struct {

	// The rules that make up the rule group.
	//
	// This member is required.
	PatchRules []PatchRule
}

// Information about the patches to use to update the instances, including target
// operating systems and source repository. Applies to Linux instances only.
type PatchSource struct {

	// The value of the yum repo configuration. For example: [main]
	//
	// name=MyCustomRepository
	//
	//     baseurl=https://my-custom-repository
	//
	// enabled=1 For
	// information about other options available for your yum repository configuration,
	// see dnf.conf(5) (https://man7.org/linux/man-pages/man5/dnf.conf.5.html).
	//
	// This member is required.
	Configuration *string

	// The name specified to identify the patch source.
	//
	// This member is required.
	Name *string

	// The specific operating system versions a patch repository applies to, such as
	// "Ubuntu16.04", "AmazonLinux2016.09", "RedhatEnterpriseLinux7.2" or "Suse12.7".
	// For lists of supported product values, see PatchFilter.
	//
	// This member is required.
	Products []string
}

// Information about the approval status of a patch.
type PatchStatus struct {

	// The date the patch was approved (or will be approved if the status is
	// PENDING_APPROVAL).
	ApprovalDate *time.Time

	// The compliance severity level for a patch.
	ComplianceLevel PatchComplianceLevel

	// The approval status of a patch (APPROVED, PENDING_APPROVAL, EXPLICIT_APPROVED,
	// EXPLICIT_REJECTED).
	DeploymentStatus PatchDeploymentStatus
}

// An aggregate of step execution statuses displayed in the AWS Console for a
// multi-Region and multi-account Automation execution.
type ProgressCounters struct {

	// The total number of steps that the system cancelled in all specified AWS Regions
	// and accounts for the current Automation execution.
	CancelledSteps int32

	// The total number of steps that failed to run in all specified AWS Regions and
	// accounts for the current Automation execution.
	FailedSteps int32

	// The total number of steps that successfully completed in all specified AWS
	// Regions and accounts for the current Automation execution.
	SuccessSteps int32

	// The total number of steps that timed out in all specified AWS Regions and
	// accounts for the current Automation execution.
	TimedOutSteps int32

	// The total number of steps run in all specified AWS Regions and accounts for the
	// current Automation execution.
	TotalSteps int32
}

// An OpsItems that shares something in common with the current OpsItem. For
// example, related OpsItems can include OpsItems with similar error messages,
// impacted resources, or statuses for the impacted resource.
type RelatedOpsItem struct {

	// The ID of an OpsItem related to the current OpsItem.
	//
	// This member is required.
	OpsItemId *string
}

// Information about targets that resolved during the Automation execution.
type ResolvedTargets struct {

	// A list of parameter values sent to targets that resolved during the Automation
	// execution.
	ParameterValues []string

	// A boolean value indicating whether the resolved target list is truncated.
	Truncated bool
}

// Compliance summary information for a specific resource.
type ResourceComplianceSummaryItem struct {

	// The compliance type.
	ComplianceType *string

	// A list of items that are compliant for the resource.
	CompliantSummary *CompliantSummary

	// Information about the execution.
	ExecutionSummary *ComplianceExecutionSummary

	// A list of items that aren't compliant for the resource.
	NonCompliantSummary *NonCompliantSummary

	// The highest severity item found for the resource. The resource is compliant for
	// this item.
	OverallSeverity ComplianceSeverity

	// The resource ID.
	ResourceId *string

	// The resource type.
	ResourceType *string

	// The compliance status for the resource.
	Status ComplianceStatus
}

// Information about the AwsOrganizationsSource resource data sync source. A sync
// source of this type can synchronize data from AWS Organizations or, if an AWS
// Organization is not present, from multiple AWS Regions.
type ResourceDataSyncAwsOrganizationsSource struct {

	// If an AWS Organization is present, this is either OrganizationalUnits or
	// EntireOrganization. For OrganizationalUnits, the data is aggregated from a set
	// of organization units. For EntireOrganization, the data is aggregated from the
	// entire AWS Organization.
	//
	// This member is required.
	OrganizationSourceType *string

	// The AWS Organizations organization units included in the sync.
	OrganizationalUnits []ResourceDataSyncOrganizationalUnit
}

// Synchronize Systems Manager Inventory data from multiple AWS accounts defined in
// AWS Organizations to a centralized S3 bucket. Data is synchronized to individual
// key prefixes in the central bucket. Each key prefix represents a different AWS
// account ID.
type ResourceDataSyncDestinationDataSharing struct {

	// The sharing data type. Only Organization is supported.
	DestinationDataSharingType *string
}

// Information about a Resource Data Sync configuration, including its current
// status and last successful sync.
type ResourceDataSyncItem struct {

	// The status reported by the last sync.
	LastStatus LastResourceDataSyncStatus

	// The last time the sync operations returned a status of SUCCESSFUL (UTC).
	LastSuccessfulSyncTime *time.Time

	// The status message details reported by the last sync.
	LastSyncStatusMessage *string

	// The last time the configuration attempted to sync (UTC).
	LastSyncTime *time.Time

	// Configuration information for the target S3 bucket.
	S3Destination *ResourceDataSyncS3Destination

	// The date and time the configuration was created (UTC).
	SyncCreatedTime *time.Time

	// The date and time the resource data sync was changed.
	SyncLastModifiedTime *time.Time

	// The name of the Resource Data Sync.
	SyncName *string

	// Information about the source where the data was synchronized.
	SyncSource *ResourceDataSyncSourceWithState

	// The type of resource data sync. If SyncType is SyncToDestination, then the
	// resource data sync synchronizes data to an S3 bucket. If the SyncType is
	// SyncFromSource then the resource data sync synchronizes data from AWS
	// Organizations or from multiple AWS Regions.
	SyncType *string
}

// The AWS Organizations organizational unit data source for the sync.
type ResourceDataSyncOrganizationalUnit struct {

	// The AWS Organization unit ID data source for the sync.
	OrganizationalUnitId *string
}

// Information about the target S3 bucket for the Resource Data Sync.
type ResourceDataSyncS3Destination struct {

	// The name of the S3 bucket where the aggregated data is stored.
	//
	// This member is required.
	BucketName *string

	// The AWS Region with the S3 bucket targeted by the Resource Data Sync.
	//
	// This member is required.
	Region *string

	// A supported sync format. The following format is currently supported: JsonSerDe
	//
	// This member is required.
	SyncFormat ResourceDataSyncS3Format

	// The ARN of an encryption key for a destination in Amazon S3. Must belong to the
	// same Region as the destination S3 bucket.
	AWSKMSKeyARN *string

	// Enables destination data sharing. By default, this field is null.
	DestinationDataSharing *ResourceDataSyncDestinationDataSharing

	// An Amazon S3 prefix for the bucket.
	Prefix *string
}

// Information about the source of the data included in the resource data sync.
type ResourceDataSyncSource struct {

	// The SyncSource AWS Regions included in the resource data sync.
	//
	// This member is required.
	SourceRegions []string

	// The type of data source for the resource data sync. SourceType is either
	// AwsOrganizations (if an organization is present in AWS Organizations) or
	// singleAccountMultiRegions.
	//
	// This member is required.
	SourceType *string

	// Information about the AwsOrganizationsSource resource data sync source. A sync
	// source of this type can synchronize data from AWS Organizations.
	AwsOrganizationsSource *ResourceDataSyncAwsOrganizationsSource

	// Whether to automatically synchronize and aggregate data from new AWS Regions
	// when those Regions come online.
	IncludeFutureRegions bool
}

// The data type name for including resource data sync state. There are four sync
// states: OrganizationNotExists (Your organization doesn't exist) NoPermissions
// (The system can't locate the service-linked role. This role is automatically
// created when a user creates a resource data sync in Explorer.)
// InvalidOrganizationalUnit (You specified or selected an invalid unit in the
// resource data sync configuration.) TrustedAccessDisabled (You disabled Systems
// Manager access in the organization in AWS Organizations.)
type ResourceDataSyncSourceWithState struct {

	// The field name in SyncSource for the ResourceDataSyncAwsOrganizationsSource
	// type.
	AwsOrganizationsSource *ResourceDataSyncAwsOrganizationsSource

	// Whether to automatically synchronize and aggregate data from new AWS Regions
	// when those Regions come online.
	IncludeFutureRegions bool

	// The SyncSource AWS Regions included in the resource data sync.
	SourceRegions []string

	// The type of data source for the resource data sync. SourceType is either
	// AwsOrganizations (if an organization is present in AWS Organizations) or
	// singleAccountMultiRegions.
	SourceType *string

	// The data type name for including resource data sync state. There are four sync
	// states: OrganizationNotExists: Your organization doesn't exist. NoPermissions:
	// The system can't locate the service-linked role. This role is automatically
	// created when a user creates a resource data sync in Explorer.
	// InvalidOrganizationalUnit: You specified or selected an invalid unit in the
	// resource data sync configuration. TrustedAccessDisabled: You disabled Systems
	// Manager access in the organization in AWS Organizations.
	State *string
}

// The inventory item result attribute.
type ResultAttribute struct {

	// Name of the inventory item type. Valid value: AWS:InstanceInformation. Default
	// Value: AWS:InstanceInformation.
	//
	// This member is required.
	TypeName *string
}

// Information about the result of a document review request.
type ReviewInformation struct {

	// The time that the reviewer took action on the document review request.
	ReviewedTime *time.Time

	// The reviewer assigned to take action on the document review request.
	Reviewer *string

	// The current status of the document review request.
	Status ReviewStatus
}

// Information about an Automation runbook (Automation document) used in a runbook
// workflow in Change Manager. The Automation runbooks specified for the runbook
// workflow can't run until all required approvals for the change request have been
// received.
type Runbook struct {

	// The name of the Automation runbook (Automation document) used in a runbook
	// workflow.
	//
	// This member is required.
	DocumentName *string

	// The version of the Automation runbook (Automation document) used in a runbook
	// workflow.
	DocumentVersion *string

	// The MaxConcurrency value specified by the user when the operation started,
	// indicating the maximum number of resources that the runbook operation can run on
	// at the same time.
	MaxConcurrency *string

	// The MaxErrors value specified by the user when the execution started, indicating
	// the maximum number of errors that can occur during the operation before the
	// updates are stopped or rolled back.
	MaxErrors *string

	// The key-value map of execution parameters, which were supplied when calling
	// StartChangeRequestExecution.
	Parameters map[string][]string

	// Information about the AWS Regions and accounts targeted by the current Runbook
	// operation.
	TargetLocations []TargetLocation

	// The name of the parameter used as the target resource for the rate-controlled
	// runbook workflow. Required if you specify Targets.
	TargetParameterName *string

	// A key-value mapping to target resources that the Runbook operation performs
	// tasks on. Required if you specify TargetParameterName.
	Targets []Target
}

// An S3 bucket where you want to store the results of this request.
type S3OutputLocation struct {

	// The name of the S3 bucket.
	OutputS3BucketName *string

	// The S3 bucket subfolder.
	OutputS3KeyPrefix *string

	// (Deprecated) You can no longer specify this parameter. The system ignores it.
	// Instead, Systems Manager automatically determines the Region of the S3 bucket.
	OutputS3Region *string
}

// A URL for the S3 bucket where you want to store the results of this request.
type S3OutputUrl struct {

	// A URL for an S3 bucket where you want to store the results of this request.
	OutputUrl *string
}

// Information about a scheduled execution for a maintenance window.
type ScheduledWindowExecution struct {

	// The time, in ISO-8601 Extended format, that the maintenance window is scheduled
	// to be run.
	ExecutionTime *string

	// The name of the maintenance window to be run.
	Name *string

	// The ID of the maintenance window to be run.
	WindowId *string
}

// The service setting data structure. ServiceSetting is an account-level setting
// for an AWS service. This setting defines how a user interacts with or uses a
// service or a feature of a service. For example, if an AWS service charges money
// to the account based on feature or service usage, then the AWS service team
// might create a default setting of "false". This means the user can't use this
// feature unless they change the setting to "true" and intentionally opt in for a
// paid feature. Services map a SettingId object to a setting value. AWS services
// teams define the default value for a SettingId. You can't create a new
// SettingId, but you can overwrite the default value if you have the
// ssm:UpdateServiceSetting permission for the setting. Use the
// UpdateServiceSetting API action to change the default setting. Or, use the
// ResetServiceSetting to change the value back to the original value defined by
// the AWS service team.
type ServiceSetting struct {

	// The ARN of the service setting.
	ARN *string

	// The last time the service setting was modified.
	LastModifiedDate *time.Time

	// The ARN of the last modified user. This field is populated only if the setting
	// value was overwritten.
	LastModifiedUser *string

	// The ID of the service setting.
	SettingId *string

	// The value of the service setting.
	SettingValue *string

	// The status of the service setting. The value can be Default, Customized or
	// PendingUpdate.
	//
	// * Default: The current setting uses a default value provisioned
	// by the AWS service team.
	//
	// * Customized: The current setting use a custom value
	// specified by the customer.
	//
	// * PendingUpdate: The current setting uses a default
	// or custom value, but a setting change request is pending approval.
	Status *string
}

// Information about a Session Manager connection to an instance.
type Session struct {

	// Reserved for future use.
	Details *string

	// The name of the Session Manager SSM document used to define the parameters and
	// plugin settings for the session. For example, SSM-SessionManagerRunShell.
	DocumentName *string

	// The date and time, in ISO-8601 Extended format, when the session was terminated.
	EndDate *time.Time

	// Reserved for future use.
	OutputUrl *SessionManagerOutputUrl

	// The ID of the AWS user account that started the session.
	Owner *string

	// The ID of the session.
	SessionId *string

	// The date and time, in ISO-8601 Extended format, when the session began.
	StartDate *time.Time

	// The status of the session. For example, "Connected" or "Terminated".
	Status SessionStatus

	// The instance that the Session Manager session connected to.
	Target *string
}

// Describes a filter for Session Manager information.
type SessionFilter struct {

	// The name of the filter.
	//
	// This member is required.
	Key SessionFilterKey

	// The filter value. Valid values for each filter key are as follows:
	//
	// *
	// InvokedAfter: Specify a timestamp to limit your results. For example, specify
	// 2018-08-29T00:00:00Z to see sessions that started August 29, 2018, and later.
	//
	// *
	// InvokedBefore: Specify a timestamp to limit your results. For example, specify
	// 2018-08-29T00:00:00Z to see sessions that started before August 29, 2018.
	//
	// *
	// Target: Specify an instance to which session connections have been made.
	//
	// *
	// Owner: Specify an AWS user account to see a list of sessions started by that
	// user.
	//
	// * Status: Specify a valid session status to see a list of all sessions
	// with that status. Status values you can specify include:
	//
	// * Connected
	//
	// *
	// Connecting
	//
	// * Disconnected
	//
	// * Terminated
	//
	// * Terminating
	//
	// * Failed
	//
	// * SessionId:
	// Specify a session ID to return details about the session.
	//
	// This member is required.
	Value *string
}

// Reserved for future use.
type SessionManagerOutputUrl struct {

	// Reserved for future use.
	CloudWatchOutputUrl *string

	// Reserved for future use.
	S3OutputUrl *string
}

// The number of managed instances found for each patch severity level defined in
// the request filter.
type SeveritySummary struct {

	// The total number of resources or compliance items that have a severity level of
	// critical. Critical severity is determined by the organization that published the
	// compliance items.
	CriticalCount int32

	// The total number of resources or compliance items that have a severity level of
	// high. High severity is determined by the organization that published the
	// compliance items.
	HighCount int32

	// The total number of resources or compliance items that have a severity level of
	// informational. Informational severity is determined by the organization that
	// published the compliance items.
	InformationalCount int32

	// The total number of resources or compliance items that have a severity level of
	// low. Low severity is determined by the organization that published the
	// compliance items.
	LowCount int32

	// The total number of resources or compliance items that have a severity level of
	// medium. Medium severity is determined by the organization that published the
	// compliance items.
	MediumCount int32

	// The total number of resources or compliance items that have a severity level of
	// unspecified. Unspecified severity is determined by the organization that
	// published the compliance items.
	UnspecifiedCount int32
}

// Detailed information about an the execution state of an Automation step.
type StepExecution struct {

	// The action this step performs. The action determines the behavior of the step.
	Action *string

	// If a step has finished execution, this contains the time the execution ended. If
	// the step has not yet concluded, this field is not populated.
	ExecutionEndTime *time.Time

	// If a step has begun execution, this contains the time the step started. If the
	// step is in Pending status, this field is not populated.
	ExecutionStartTime *time.Time

	// Information about the Automation failure.
	FailureDetails *FailureDetails

	// If a step failed, this message explains why the execution failed.
	FailureMessage *string

	// Fully-resolved values passed into the step before execution.
	Inputs map[string]string

	// The flag which can be used to help decide whether the failure of current step
	// leads to the Automation failure.
	IsCritical bool

	// The flag which can be used to end automation no matter whether the step succeeds
	// or fails.
	IsEnd bool

	// The maximum number of tries to run the action of the step. The default value is
	// 1.
	MaxAttempts int32

	// The next step after the step succeeds.
	NextStep *string

	// The action to take if the step fails. The default value is Abort.
	OnFailure *string

	// Returned values from the execution of the step.
	Outputs map[string][]string

	// A user-specified list of parameters to override when running a step.
	OverriddenParameters map[string][]string

	// A message associated with the response code for an execution.
	Response *string

	// The response code returned by the execution of the step.
	ResponseCode *string

	// The unique ID of a step execution.
	StepExecutionId *string

	// The name of this execution step.
	StepName *string

	// The execution status for this step.
	StepStatus AutomationExecutionStatus

	// The combination of AWS Regions and accounts targeted by the current Automation
	// execution.
	TargetLocation *TargetLocation

	// The targets for the step execution.
	Targets []Target

	// The timeout seconds of the step.
	TimeoutSeconds int64

	// Strategies used when step fails, we support Continue and Abort. Abort will fail
	// the automation when the step fails. Continue will ignore the failure of current
	// step and allow automation to run the next step. With conditional branching, we
	// add step:stepName to support the automation to go to another specific step.
	ValidNextSteps []string
}

// A filter to limit the amount of step execution information returned by the call.
type StepExecutionFilter struct {

	// One or more keys to limit the results. Valid filter keys include the following:
	// StepName, Action, StepExecutionId, StepExecutionStatus, StartTimeBefore,
	// StartTimeAfter.
	//
	// This member is required.
	Key StepExecutionFilterKey

	// The values of the filter key.
	//
	// This member is required.
	Values []string
}

// Metadata that you assign to your AWS resources. Tags enable you to categorize
// your resources in different ways, for example, by purpose, owner, or
// environment. In Systems Manager, you can apply tags to documents, managed
// instances, maintenance windows, Parameter Store parameters, and patch baselines.
type Tag struct {

	// The name of the tag.
	//
	// This member is required.
	Key *string

	// The value of the tag.
	//
	// This member is required.
	Value *string
}

// An array of search criteria that targets instances using a Key,Value combination
// that you specify. One or more targets must be specified for maintenance window
// Run Command-type tasks. Depending on the task, targets are optional for other
// maintenance window task types (Automation, AWS Lambda, and AWS Step Functions).
// For more information about running tasks that do not specify targets, see
// Registering maintenance window tasks without targets
// (https://docs.aws.amazon.com/systems-manager/latest/userguide/maintenance-windows-targetless-tasks.html)
// in the AWS Systems Manager User Guide. Supported formats include the
// following.
//
// *
// Key=InstanceIds,Values=instance-id-1,instance-id-2,instance-id-3
//
// *
// Key=tag:my-tag-key,Values=my-tag-value-1,my-tag-value-2
//
// *
// Key=tag-key,Values=my-tag-key-1,my-tag-key-2
//
// * Run Command and Maintenance
// window targets only: Key=resource-groups:Name,Values=resource-group-name
//
// *
// Maintenance window targets only:
// Key=resource-groups:ResourceTypeFilters,Values=resource-type-1,resource-type-2
//
// *
// Automation targets only: Key=ResourceGroup;Values=resource-group-name
//
// For
// example:
//
// *
// Key=InstanceIds,Values=i-02573cafcfEXAMPLE,i-0471e04240EXAMPLE,i-07782c72faEXAMPLE
//
// *
// Key=tag:CostCenter,Values=CostCenter1,CostCenter2,CostCenter3
//
// *
// Key=tag-key,Values=Name,Instance-Type,CostCenter
//
// * Run Command and Maintenance
// window targets only: Key=resource-groups:Name,Values=ProductionResourceGroup
// This example demonstrates how to target all resources in the resource group
// ProductionResourceGroup in your maintenance window.
//
// * Maintenance window
// targets only:
// Key=resource-groups:ResourceTypeFilters,Values=AWS::EC2::INSTANCE,AWS::EC2::VPC
// This example demonstrates how to target only EC2 instances and VPCs in your
// maintenance window.
//
// * Automation targets only:
// Key=ResourceGroup,Values=MyResourceGroup
//
// * State Manager association targets
// only: Key=InstanceIds,Values=*  This example demonstrates how to target all
// managed instances in the AWS Region where the association was created.
//
// For more
// information about how to send commands that target instances using Key,Value
// parameters, see Targeting multiple instances
// (https://docs.aws.amazon.com/systems-manager/latest/userguide/send-commands-multiple.html#send-commands-targeting)
// in the AWS Systems Manager User Guide.
type Target struct {

	// User-defined criteria for sending commands that target instances that meet the
	// criteria.
	Key *string

	// User-defined criteria that maps to Key. For example, if you specified
	// tag:ServerRole, you could specify value:WebServer to run a command on instances
	// that include EC2 tags of ServerRole,WebServer.
	Values []string
}

// The combination of AWS Regions and accounts targeted by the current Automation
// execution.
type TargetLocation struct {

	// The AWS accounts targeted by the current Automation execution.
	Accounts []string

	// The Automation execution role used by the currently running Automation. If not
	// specified, the default value is AWS-SystemsManager-AutomationExecutionRole.
	ExecutionRoleName *string

	// The AWS Regions targeted by the current Automation execution.
	Regions []string

	// The maximum number of AWS accounts and AWS regions allowed to run the Automation
	// concurrently.
	TargetLocationMaxConcurrency *string

	// The maximum number of errors allowed before the system stops queueing additional
	// Automation executions for the currently running Automation.
	TargetLocationMaxErrors *string
}

// Code generated by smithy-go-codegen DO NOT EDIT.

// Package sts provides the API client, operations, and parameter types for AWS
// Security Token Service.
//
// AWS Security Token Service AWS Security Token Service (STS) enables you to
// request temporary, limited-privilege credentials for AWS Identity and Access
// Management (IAM) users or for users that you authenticate (federated users).
// This guide provides descriptions of the STS API. For more information about
// using this service, see Temporary Security Credentials
// (https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_temp.html).
package sts

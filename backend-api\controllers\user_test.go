package controllers

import (
	"context"
	"fmt"
	"testing"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
)

var ac User

func init() {
	// ac.Pre()
}

func TestIndex(t *testing.T) {
	ret, err := ac.Index(context.Background(), Empty{}, UserFilters{}, Empty{})
	if err != nil {
		t.<PERSON>rror(err)
	}
	fmt.Printf("%+v\n", ret)
}

func TestCreate(t *testing.T) {
	var ai models.UserInput
	ai.FirstName = String("Test")
	ai.MiddleName = String("Q")
	ai.LastName = String("User")
	ai.Password = String("Password")
	ai.Email = String("<EMAIL>")

	ret, err := ac.Create(context.Background(), Empty{}, Empty{}, ai)
	if err != nil {
		t.Error(err)
	}
	fmt.Printf("%+v\n", ret)
}

func TestUpdate(t *testing.T) {
	var fields models.UserInput
	// fields.PhoneNumber = String("55556")
	ret, err := ac.Update(context.Background(), ModelID{ID: "1"}, Empty{}, fields)
	if err != nil {
		t.Error(err)
	}
	if _, ok := ret.(*models.User); !ok {
		t.Error("Expected result to be User")
	}
}

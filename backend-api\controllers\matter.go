package controllers

import (
	"context"
	"slices"
	"strings"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
	"github.com/Matterhorn-Back-Office-Solutions/backend-api/scopes"
	"github.com/ontologics/parmenides/auth"
	"gorm.io/gorm"
)

type MatterFilters struct {
	ClientId          int
	ParentId          int
	Number            string
	ClientName        string
	ClientLongName    string
	ParentName        string
	HasEntries        bool
	ApplicationNumber string
	ApplicationTitle  string
	Type              string
	Name              string
	BillerIds         string
	models.Paging
	models.Sort
	Minimal        bool
	EntryStartDate string
	EntryEndDate   string
}

func (f *MatterFilters) restrictToOwn(ctx context.Context, db *gorm.DB) *models.Restriction {
	uc := ctx.Value(auth.CLAIMS).(models.Claims)
	if slices.Contains(uc.Scope, string(scopes.MATTER_VIEW_OWN)) {
		res := []models.Restriction{}
		var ids []string
		db.Raw("select distinct clients.id from matters join clients on (matters.client_id = clients.id) where clients.user1_id = ?", uc.User.ID).Scan(&ids)
		if len(ids) > 0 {
			res = append(res, models.Restriction{Field: "matters.id", Value: ids, Operator: "in"})
		}
		db.Raw("select m.id from rates r join matters m on (r.client_id = m.client_id) where user_id = ? or user_id = 0", uc.User.ID).Scan(&ids)
		if len(ids) > 0 {
			res = append(res, models.Restriction{Field: "matters.id", Value: ids, Operator: "in", Joiner: models.OR})
		}
		return &models.Restriction{Joiner: models.AND, Restrictions: &res}
	}
	return nil
}
func (mf *MatterFilters) PageAndSearch(ctx context.Context, db *gorm.DB) (ps PageAndSearch, preload []any) {
	ps = PageAndSearch{Paging: mf.Paging, Sort: mf.Sort}
	r := mf.restrictToOwn(ctx, db)
	if r != nil {
		ps.Restrictions = append(ps.Restrictions, *r)
	}

	ps.Sort.OrderBy = models.GetFieldName(ps.Sort.OrderBy)
	ps.Search = make(models.Search)
	if mf.ClientId != 0 {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "client_id", Operator: "=", Value: mf.ClientId, Joiner: models.AND})
	}
	if mf.Name != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: `concat(matters.number, ' - ', matters.application_title )`, Operator: "like", Value: "%" + strings.ReplaceAll(mf.Name, " - ", "") + "%", Joiner: models.AND})
	}
	if mf.ParentId != 0 {
		preload = append(preload, "Client")
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "ifnull(Client.parent_id,Client.id)", Operator: "=", Value: mf.ParentId, Joiner: models.AND})
	}
	if mf.Number != "" {
		ps.Search["matters.number"] = mf.Number
	}
	if mf.ClientName != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "Client.name", Operator: "like", Value: "%" + mf.ClientName + "%", Joiner: models.AND})
	}
	if mf.ParentName != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "Client__Parent.name", Operator: "like", Value: "%" + mf.ParentName + "%", Joiner: models.AND})
	}
	if mf.ApplicationNumber != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "application_number", Operator: "like", Value: "%" + mf.ApplicationNumber + "%", Joiner: models.AND})
	}
	if mf.ApplicationTitle != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "application_title", Operator: "like", Value: "%" + mf.ApplicationTitle + "%", Joiner: models.AND})
	}
	if mf.Type != "" {
		types := strings.Split(mf.Type, ",")
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "type", Operator: "in", Value: types, Joiner: models.AND})
	}
	if mf.ClientLongName != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: `concat(Client.number,ifnull(Client__Parent.name,""), Client.name)`,
			Operator: "like", Value: "%" + strings.ReplaceAll(mf.ClientLongName, " - ", "") + "%", Joiner: models.AND})
	}
	if mf.HasEntries {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "matters.entry_count", Operator: ">", Value: 0, Joiner: models.AND})
	}
	if mf.BillerIds != "" {
		ids := strings.Split(mf.BillerIds, ",")
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "Client.biller_id", Operator: "in", Value: ids, Joiner: models.AND})
	}
	if !mf.Minimal {
		preload = append(preload, "Client", "Client.Parent", "Client.Biller")
	}

	if mf.EntryEndDate != "" {
		query := db.WithContext(ctx).Table("entries").Select("count(*) as filtered_entry_count, m.id as mid").
			Joins("left join matters m on m.id = entries.matter_id").Where("entries.date <=? and entries.invoice_id is null and entries.is_billable = true and entries.is_ready=true", mf.EntryEndDate).Group("m.id")
		preload = append(preload, models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
			return db.Select("matters.*, filtered_entry_count as entry_count").Joins("join (?) q on (q.mid = matters.id)", query)
		}})
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "filtered_entry_count", Operator: ">", Value: 0, Joiner: models.AND})
		if ps.Sort.OrderBy == "entry_count" {
			ps.Sort.OrderBy = "filtered_entry_count"
		}
	}

	return
}

type Matter struct {
	Base
}

func (c *Matter) Index(ctx context.Context, urlParams Empty, queryParams MatterFilters, body Empty) (any, error) {
	var model models.Matters
	return c.List(ctx, &queryParams, &model, false, nil, nil)
}

func (c *Matter) Create(ctx context.Context, urlParams Empty, queryParams Empty, body models.MatterInput) (any, error) {
	m := &models.Matter{MatterInput: body}
	if err := c.Maker.Create(ctx, m, nil); err != nil {
		return nil, ConvertToParmenidesError(err)
	}
	return m, nil
}

func (c *Matter) Update(ctx context.Context, urlParams ModelID, queryParams Empty, body models.MatterInput) (any, error) {
	var m models.Matter
	tx := c.Maker.DB().WithContext(ctx).Begin()
	m2 := models.Matter{MatterInput: body}
	ret, err := c.Change(ctx, urlParams.GetID(), &m, &m2, tx)
	m.ID = urlParams.GetID()
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	err = tx.Model(&m).Association("Tags").Replace(body.Tags)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	return ret, tx.Commit().Error

}

func (c *Matter) Read(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var matter models.Matter
	return c.Get(ctx, &matter, uint(urlParams.GetID()), false, []string{"Client", "Client.Parent", "Tags"})
}
func (c *Matter) Name(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var ret models.Matter
	return ret.Number + " - " + ret.ApplicationTitle, ConvertToParmenidesError(c.Maker.DB().WithContext(ctx).Where("id=?", urlParams.GetID()).First(&ret).Error)

}

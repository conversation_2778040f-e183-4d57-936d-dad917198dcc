package controllers

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
	"github.com/Matterhorn-Back-Office-Solutions/backend-api/scopes"
	"github.com/Matterhorn-Back-Office-Solutions/backend-api/utils"
	"github.com/ontologics/parmenides"
	"github.com/ontologics/parmenides/auth"
	perrors "github.com/ontologics/parmenides/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type InvoiceFilters struct {
	Id                   string
	ClientId             int
	Number               string
	ClientName           string
	ClientLongName       string
	ParentName           string
	MatterName           string
	StatusId             string
	UserId               string
	BillerIds            string
	IncludeUnpaidEntries bool
	models.Paging
	models.Sort
	Minimal bool
}

func (f *InvoiceFilters) restrictToOwn(ctx context.Context, db *gorm.DB) *models.Restriction {
	uc := ctx.Value(auth.CLAIMS).(models.Claims)
	if slices.Contains(uc.Scope, string(scopes.ENTRY_OWN)) {
		var uids []string
		db.WithContext(ctx).Raw("select matters.id from matters join clients on (matters.client_id=clients.id) where clients.user1_id = ?", uc.User.ID).Scan(&uids)
		if len(uids) > 0 {
			return &models.Restriction{Field: "Matter.id", Value: uids, Operator: "in", Joiner: models.AND}
		}
		db.WithContext(ctx).Raw("select m.id from matters m join rates r on (m.client_id = r.client_id) where r.user_id=? or r.user_id = 0", uc.User.ID).Scan(&uids)
		return &models.Restriction{Field: "Matter.id", Value: uids, Operator: "in", Joiner: models.AND}
	}
	return nil
}

func (invf *InvoiceFilters) PageAndSearch(ctx context.Context, db *gorm.DB) (ps PageAndSearch, preload []any) {

	ps = PageAndSearch{Paging: invf.Paging, Sort: invf.Sort}
	r := invf.restrictToOwn(ctx, db)
	if r != nil {
		ps.Restrictions = append(ps.Restrictions, *r)
	}
	if invf.Number != "" {
		ps.Search["Client.number"] = invf.Number
	}

	if invf.Id != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "invoices.id", Operator: "=", Value: invf.Id, Joiner: models.AND})

	}

	if invf.BillerIds != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "Client.biller_id", Operator: "=", Value: invf.BillerIds, Joiner: models.AND})
	}

	if invf.ClientLongName != "" {
		ps.Restrictions = append(ps.Restrictions,
			models.Restriction{Field: `concat(Client.number,ifnull(Client__Parent.name,""), Client.name)`, Operator: "like", Value: "%" + strings.ReplaceAll(invf.ClientLongName, " - ", "") + "%", Joiner: models.AND})
	}

	if invf.StatusId != "" {
		parts := strings.Split(invf.StatusId, ",")
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "status_id", Operator: "in", Value: parts, Joiner: models.AND})
	}

	if invf.ClientId != 0 {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "invoices.client_id", Operator: "=", Value: invf.ClientId, Joiner: models.AND})
	}

	if invf.UserId != "" {
		re := []models.Restriction{}
		re = append(re, models.Restriction{Field: "Client.biller_id", Operator: "=", Value: invf.UserId},
			models.Restriction{Field: "Client.user1_id", Operator: "=", Value: invf.UserId, Joiner: models.OR})
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Restrictions: &re, Joiner: models.AND})
	}

	if invf.MatterName != "" {
		ps.Restrictions = append(ps.Restrictions,
			models.Restriction{Field: `concat(Matter.number,Matter.application_title)`, Operator: "like", Value: "%" + strings.ReplaceAll(invf.MatterName, " - ", "") + "%", Joiner: models.AND})
	}

	if !invf.Minimal {
		preload = []any{"Client", "Status", "Matter", "Client.Biller", "Client.Parent", "Client.User1"}
	} else {
		preload = []any{
			"Status",
			models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
				return db.Joins("Client", db.Select("Client.name", "Client.number", "client.biller_id"))
			}},
			models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
				return db.Joins("Client.Parent", db.Select("Client__Parent.name", "Client__Parent.number"))
			}},
			models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
				return db.Joins("Client.Biller", db.Select("Client__Biller.first_name", "Client__Biller.last_name"))
			}},
			models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
				return db.Joins("Matter", db.Select("Matter.number", "Matter.application_title"))
			}},
		}
	}

	if invf.IncludeUnpaidEntries {
		preload = append(preload,
			models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
				return db.Preload("Entries", func(db *gorm.DB) *gorm.DB {
					return db.Joins("User", db.Select("User.initials")).Order("entries.type asc, entries.date asc")
				})
			}})
		ps.Paging.Limit = 10000
		ps.Sort.OrderBy = "invoices.date"
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "status_id", Operator: ">", Value: 3, Joiner: models.AND})
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "entry_ids", Operator: "!=", Value: "", Joiner: models.AND})
		//TODO: figure out why this is happening
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "entry_ids", Operator: "!=", Value: "0", Joiner: models.AND})
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "status_id", Operator: "<", Value: 6, Joiner: models.AND})
	}
	return
}

type Invoice struct {
	Base
}

type NextID struct {
	Previous *uint `json:"previous"`
	Next     *uint `json:"next"`
}

type TEs struct {
	Items []models.Entry `json:"items"`
}

type BulkIDs struct {
	Clients []struct {
		ID        uint   `json:"id"`
		MatterIDs []uint `json:"matterIds"`
	} `json:"clients"`
	StartDate *time.Time `json:"startDate"`
	EndDate   *time.Time `json:"endDate"`
}

type BulkInvoiceIds struct {
	InvoiceIDs []uint `json:"invoiceIds"`
}

type ClientIDs struct {
	IDs []uint `json:"ids"`
}

func (c *Invoice) Index(ctx context.Context, urlParams Empty, queryParams InvoiceFilters, body Empty) (any, error) {
	var model models.Invoices

	ret, err := c.List(ctx, &queryParams, &model, false, nil, nil)
	return ret, err
}

func (c *Invoice) Statuses(ctx context.Context, urlParams Empty, queryParams PageAndSearch, body Empty) (any, error) {
	var model models.InvoiceStatuses
	return c.List(ctx, queryParams, &model, false, nil, nil)
}

func (c *Invoice) Next(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var nextID NextID

	var m models.Invoice
	c.Maker.Get(ctx, &m, uint(urlParams.GetID()), false, []string{"Matter", "Status", "Client"})
	if m.ID == 0 {
		return nil, perrors.NotFoundError{Msg: "invoice not found"}
	}

	if m.Status.ApprovalLevel == models.MANAGING_ATTORNEY {
		err := c.Maker.DB().WithContext(ctx).Raw(
			"select min(invoices.id) as next from invoices join clients c on (invoices.client_id = c.id) where status_id = ? and c.user1_id =? and invoices.id!=?",
			m.StatusID, m.Client.User1ID, m.ID).Scan(&nextID).Error
		return &nextID, ConvertToParmenidesError(err)
	}

	err := c.Maker.DB().WithContext(ctx).Raw(
		"select min(invoices.id) as next from invoices join clients c on (invoices.client_id = c.id) where status_id = ? and c.biller_id=? and invoices.id!=?",
		m.StatusID, m.Client.BillerID, m.ID).Scan(&nextID).Error

	return &nextID, ConvertToParmenidesError(err)
}

func (c *Invoice) Create(ctx context.Context, urlParams Empty, queryParams Empty, body models.InvoiceInput) (any, error) {
	m := &models.Invoice{InvoiceInput: body}
	if err := c.Maker.Create(ctx, m, nil); err != nil {
		return nil, ConvertToParmenidesError(err)
	}
	return m, nil
}

func (c *Invoice) sendInvoicesForReview(ctx context.Context, tx *gorm.DB, clientID uint, matterID uint, invoiceID uint, statusID uint) error {
	req := ctx.Value("Header").(http.Header)
	baseURL := GetBaseURL(req.Get("Referer"))
	var count int64
	if err := tx.Model(&models.Invoice{}).Where("status_id < ? and client_id =?", statusID, clientID).Count(&count).Error; err != nil {
		return ConvertToParmenidesError(err)
	}

	if count > 1 {
		return nil
	}
	var status models.InvoiceStatus
	tx.First(&status, statusID)

	var id uint
	if status.ID != 0 && status.ID < 4 {
		var matter models.Matter
		tx.Preload("Client").Preload("Client.Parent").First(&matter, matterID)
		var note models.NotificationInput
		if matter.Client.Parent != nil {
			note.Title = fmt.Sprintf("Invoices for %s - %s are ready for your review", matter.Client.Parent.Name, matter.Client.Name)
		} else {
			note.Title = fmt.Sprintf("Invoices for %s are ready for your review", matter.Client.Name)
		}
		switch status.ApprovalLevel {
		case models.WORKING_ATTORNEY:
			id = *matter.User2ID
		case models.MANAGING_ATTORNEY:
			var client models.Client
			client.ID = clientID
			tx.Select("user1_id").Find(&client)
			id = *client.User1ID
			note.Body = fmt.Sprintf("%s<br/><a href=\"%s\"/>Click here to review</a><br/>You have two (2) business days to review.", note.Title, baseURL)
		case models.BILLING:
			var client models.Client
			client.ID = clientID
			tx.Select("biller_id").Find(&client)
			id = *client.BillerID
		}

		note.UserID = id
		GetNotificationController().Create(ctx, Empty{}, Empty{}, note)
	}
	return nil
}

func (c *Invoice) sendEntriesForReview(ctx context.Context, invoiceID uint, statusID uint) error {
	var status models.InvoiceStatus
	c.Maker.Get(ctx, &status, statusID, false, nil)
	if status.ApprovalLevel != models.MANAGING_ATTORNEY {
		return nil
	}
	var users []uint
	c.Maker.DB().WithContext(ctx).Raw(
		`SELECT user_id FROM entries
		join users on (entries.user_id=users.id)
		join invoices on (invoices.id=entries.invoice_id)
		WHERE user_id in (SELECT user_id FROM entries WHERE invoice_id = ?)
		AND status_id < 3 and users.deleted_at is null and invoices.deleted_at is null 
		GROUP BY user_id`, invoiceID).Scan(&users)

	for _, u := range users {
		var note models.NotificationInput
		note.UserID = u
		note.Title = "Invoice Entries Ready for Your Review"
		note.Body = "Please review your your time entries on pending invoices"
		GetNotificationController().Create(ctx, Empty{}, Empty{}, note)
	}
	return nil
}

func (c *Invoice) Update(ctx context.Context, urlParams ModelID, queryParams Empty, body models.InvoiceInput) (any, error) {
	var o, n models.Invoice
	c.Get(ctx, &o, urlParams.GetID(), false, []string{"Client"})
	if o.ID == 0 {
		return nil, perrors.NotFoundError{}
	}

	uc := ctx.Value(auth.CLAIMS).(models.Claims)
	if slices.Contains(uc.Scope, string(scopes.INVOICE_VIEW_OWN)) {
		if *o.Client.User1ID != uint(uc.User.ID) {
			return nil, perrors.PermissionDeniedError{}
		}
	}

	if body.StatusID == 0 {
		body.StatusID = o.StatusID
	}
	n = o
	n.InvoiceInput = body
	if n.ClientID == 0 {
		n.ClientID = o.ClientID
	}
	if n.MatterID == 0 {
		n.MatterID = o.MatterID
	}
	tx := c.Maker.DB().Begin().WithContext(ctx)

	err := c.Save(tx, &o, &n)
	if err != nil {
		tx.Rollback()
		return nil, ConvertToParmenidesError(err)
	}

	// if status changed update required people
	if o.StatusID != body.StatusID {
		err = c.sendInvoicesForReview(ctx, tx, o.ClientID, o.MatterID, urlParams.GetID(), body.StatusID)
		if err != nil {
			tx.Rollback()
			return nil, ConvertToParmenidesError(err)
		}
	}

	err = tx.Commit().Error
	return n, ConvertToParmenidesError(err)
}

func (c *Invoice) GetItems(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	db := c.Maker.DB().WithContext(ctx)
	var currency models.Currency
	db.Joins("join clients on (currencies.id = clients.currency_id) join invoices on (invoices.client_id=clients.id)").Where("invoices.id=?", urlParams.GetID()).First(currency)
	var m models.Entries
	err := c.Maker.DB().WithContext(ctx).Where("invoice_id=?", urlParams.GetID()).Find(&m).Error
	if err != nil {
		return nil, ConvertToParmenidesError(err)
	}
	for i := 0; i < len(m); i++ {
		m[i].RateClientCurrency = m[i].Currency.Convert(&currency, m[i].Rate)
		m[i].TotalClientCurrency = m[i].Currency.Convert(&currency, m[i].Total)
	}
	return m, nil
}

func (c *Invoice) Read(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var Invoice models.Invoice
	c.Maker.Get(ctx, &Invoice, uint(urlParams.GetID()), false, []string{"Client", "Client.Parent", "Matter", "Status"})
	if Invoice.ID == 0 {
		return nil, perrors.NotFoundError{Msg: "invoice not found"}
	}

	return Invoice, nil
}

func (c *Invoice) Format(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	_, ret, err := c.format(ctx, urlParams)
	return ret, err
}

func (c *Invoice) format(ctx context.Context, id ModelID) (string, string, error) {

	var invoice models.Invoice
	c.Maker.Get(ctx, &invoice, uint(id.GetID()), false, []string{"Client", "Client.Parent", "Client.Discount", "Client.Parent.Discount", "Matter", "Entries", "Entries.User", "Entries.PhaseTaskCode", "Entries.ActivityExpenseCode", "Role"})
	if invoice.ID == 0 {
		return "", "", perrors.NotFoundError{Msg: "invoice not found"}
	}
	return invoice.Format(c.Maker.DB().WithContext(ctx), id.GetID())
}

func (c *Invoice) FormatBulk(ctx context.Context, urlParams Empty, queryParams Empty, body BulkInvoiceIds) (any, error) {
	files := []struct{ Name, Body string }{}

	for _, i := range body.InvoiceIDs {
		n, s, err := c.format(ctx, ModelID{ID: fmt.Sprintf("%d", i)})
		if err != nil {
			return nil, err
		}
		files = append(files, struct {
			Name string
			Body string
		}{n, s})
	}
	ret := parmenides.ResultWithCustomHeader{}
	var err error

	ret.Body, err = zipFiles(files)
	ret.Header = map[string]string{"Content-Type": "application/zip"}
	return ret, ConvertToParmenidesError(err)
}

func (c *Invoice) Hold(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var Invoice models.Invoice
	var err error
	c.Maker.Get(ctx, &Invoice, uint(urlParams.GetID()), false, nil)
	if Invoice.ID == 0 {
		return nil, perrors.NotFoundError{Msg: "invoice not found"}
	}
	tx := c.Maker.DB().WithContext(ctx).Begin()

	err = tx.Exec("update entries set invoice_id = null where invoice_id = ?", urlParams.GetID()).Error
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	err = tx.Exec("update invoices set deleted_at = current_timestamp(), entry_ids = '' where id = ?", urlParams.GetID()).Error
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	var m models.Matter
	m.ID = Invoice.MatterID
	var client models.Client
	client.ID = Invoice.ClientID

	err = m.UpdateEntryCount(tx)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	err = client.UpdateEntryCount(tx)
	if err != nil {
		tx.Rollback()
		return nil, err
	}
	return nil, tx.Commit().Error
}

func zipFiles(files []struct{ Name, Body string }) ([]byte, error) {
	buf := new(bytes.Buffer)
	zipWriter := zip.NewWriter(buf)
	for _, file := range files {
		fileWriter, err := zipWriter.Create(file.Name)
		if err != nil {
			return nil, err
		}
		_, err = io.WriteString(fileWriter, file.Body)
		if err != nil {
			return nil, err
		}
	}
	err := zipWriter.Close()
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil

}

type MatterRet struct {
	Number    string   `json:"number"`
	InvoiceID *uint    `json:"invoiceId"`
	Errors    []string `json:"errors"`
}

type ClientRet struct {
	Number  string      `json:"number"`
	Matters []MatterRet `json:"matters"`
	Errors  []string    `json:"errors"`
}

type BulkCreateResult struct {
	Clients []ClientRet `json:"clients"`
}

func (c *Invoice) createLineItems(ctx context.Context, m *models.Matter, inv *models.Invoice, start *time.Time, end *time.Time) (*MatterRet, error) {
	db := c.Maker.DB().WithContext(ctx)
	mr := MatterRet{Number: m.Number}
	binds := []any{m.ID}
	where := "is_billable = true and is_ready = true and invoice_id is null and matter_id=?"
	if start != nil {
		where += " and date >= ?"
		binds = append(binds, start)
	}
	if end != nil {
		where += " and date <= ?"
		binds = append(binds, end)
	}

	err := db.Model(models.Entry{}).Where(where, binds...).Find(&inv.Entries).Error
	if len(inv.Entries) == 0 {
		return nil, nil
	}
	if err != nil {
		mr.Errors = append(mr.Errors, "failed to pull active matters for client")
		return &mr, err
	}
	n := time.Now()
	inv.LastStatusChange = n
	inv.PreviousStatusChange = n
	for j := range inv.Entries {
		inv.EntryIDs += fmt.Sprintf("%d", inv.Entries[j].ID)
		if j < len(inv.Entries)-1 {
			inv.EntryIDs += ","
		}
	}
	err = c.Maker.Create(ctx, &inv, nil)
	mr.InvoiceID = &inv.ID
	if err != nil {
		return &mr, err
	}
	return &mr, nil
}

func (c *Invoice) BulkCreate(ctx context.Context, urlparams Empty, queryParams Empty, body BulkIDs) (*BulkCreateResult, error) {
	var ret BulkCreateResult
	db := c.Maker.DB().WithContext(ctx)

	for i, cl := range body.Clients {

		var client models.Client

		db.Select("number").Where("id=?", cl.ID).Find(&client)

		ret.Clients = append(ret.Clients, ClientRet{Number: client.Number})
		var ms []models.Matter
		var err error

		//grab all matters with entries

		if len(cl.MatterIDs) > 0 {
			err = db.Where("client_id = ? and entry_count>0 and id in ?", cl.ID, cl.MatterIDs).Find(&ms).Error
		} else {
			err = db.Where("client_id = ? and entry_count>0", cl.ID).Find(&ms).Error
		}
		if err != nil {
			ret.Clients[i].Errors = append(ret.Clients[i].Errors, fmt.Sprintf("failed to pull active matters for client: %s", ""))
		}
		if len(ms) < 1 {
			continue
		}

		for _, m := range ms {
			now := time.Now()
			inv := models.Invoice{InvoiceInput: models.InvoiceInput{ClientID: cl.ID, StatusID: 1, Date: &now, MatterID: m.ID}}
			mr, _ := c.createLineItems(ctx, &m, &inv, body.StartDate, body.EndDate)
			ret.Clients[i].Matters = append(ret.Clients[i].Matters, *mr)
		}
	}
	return &ret, nil
}

func (c *Invoice) PDF(ctx context.Context, urlParams ModelID, queryParams struct{ Access_token string }, body Empty) (any, error) {
	tx := c.Maker.DB().WithContext(ctx)
	log.Printf("queryParams.Access_token: %#+v\n", queryParams.Access_token)
	var data models.Invoice
	data.ID = urlParams.GetID()
	c.Maker.Get(ctx, &data, uint(urlParams.GetID()), true,
		[]string{"Matter", "Entries.PhaseTaskCode", "Entries.ActivityExpenseCode", "Entries.Currency", "Entries.User",
			"Client.Parent", "Client.Currency", "Client.Contacts"})
	if data.ID == 0 {
		return nil, perrors.PermissionDeniedError{}
	}
	var err error
	ret := parmenides.ResultWithCustomHeader{}
	var pid, iid uint
	if data.Client.ParentID != nil {
		pid = *data.Client.ParentID
		iid = *data.Client.Parent.InvoiceConfigurationID
	} else {
		pid = data.ClientID
		iid = *data.Client.InvoiceConfigurationID
	}
	data.LoadTaskCodeSets(tx)
	ret.Body, err = data.PDF(c.loadClientConfiguration(ctx, iid), c.loadDiscount(ctx, pid), tx)

	ret.Header = map[string]string{"Content-Type": "application/pdf"}
	return ret, ConvertToParmenidesError(err)
}

func (c *Invoice) loadDiscount(ctx context.Context, clientID uint) models.Discount {
	var discount models.Discount
	c.Maker.DB().WithContext(ctx).Where("id=?", clientID).First(&discount)
	return discount
}

func (c *Invoice) loadClientConfiguration(ctx context.Context, clientID uint) models.ClientInvoiceConfiguration {
	var config models.ClientInvoiceConfiguration
	c.Maker.DB().WithContext(ctx).Where("id=?", clientID).Preload(clause.Associations).First(&config)
	return config
}

type DateRange struct {
	Start string
	End   string
}

const DATEFORMAT = "2006-01-02T15:04:05"

func (d *DateRange) StartDate() time.Time {
	ret, err := time.Parse(DATEFORMAT, d.Start)
	if err != nil {
		ret = time.Now().Add(-30 * 24 * time.Hour)
	}
	return ret

}

func (d *DateRange) EndDate() time.Time {
	ret, err := time.Parse(DATEFORMAT, d.End)
	if err != nil {
		ret = time.Now()
	}
	return ret
}

func (c *Invoice) ChangeLog(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var ret []*models.InvoiceDelta
	var logs models.AuditLogs
	var current, previous models.Invoice
	c.Maker.DB().WithContext(ctx).Where("object_id=? and table_name=?", urlParams.ID, "invoices").Order("created_at asc").Find(&logs)
	json.Unmarshal(logs[0].Data, &previous)
	for i := 1; i < len(logs); i++ {
		json.Unmarshal(logs[i].Data, &current)
		var a models.User
		id, _ := strconv.ParseUint(logs[i].UserId, 10, 64)

		c.Maker.Get(ctx, &a, uint(id), false, nil)

		if d := previous.Delta(&current, a.Name(), logs[i].CreatedAt, current.ChangeComment); d != nil {
			var s []models.InvoiceStatus
			if d.Status != nil {
				order := "asc"
				if d.Status.New.(uint) < d.Status.Old.(uint) {
					order = "desc"
				}
				c.Maker.DB().WithContext(ctx).Order("id "+order).Find(&s, []any{d.Status.Old, d.Status.New})
				d.Status.Old = s[0].Name
				d.Status.New = s[1].Name
			}

			ret = append(ret, d)
		}
		previous = current
	}
	return ret, nil

}

type change struct {
	When          time.Time
	Type          int
	Who           string
	ChangeComment string
}

// TODO: refactor this to be less ugly
func (c *Invoice) Delta(ctx context.Context, urlParams ModelID, queryParams DateRange, body Empty) (any, error) {
	db := c.Maker.DB().WithContext(ctx)
	var current, next, start models.Invoice
	var logs models.AuditLogs
	var ret models.InvoiceDelta

	c.Maker.Get(ctx, &current, urlParams.GetID(), false, nil)
	if current.ID == 0 {
		return nil, perrors.NotFoundError{Msg: "invoice not found"}
	}

	sd := queryParams.StartDate()
	if queryParams.Start == "" {
		sd = current.PreviousStatusChange
	}
	db.Where("object_id=? and table_name=? and created_at >= ?", urlParams.ID, "invoices", sd).Order("created_at asc").Find(&logs)
	if len(logs) == 0 {
		return nil, nil
	}
	json.Unmarshal(logs[0].Data, &start)
	eids := make(map[uint64]change)
	se := strings.Split(start.EntryIDs, ",")

	for i := 1; i < len(logs); i++ {
		json.Unmarshal(logs[i].Data, &next)
		d1 := start.Delta(&next, "", time.Now(), "")
		if d1 == nil {
			continue
		}
		if d1.Date != nil {
			if ret.Date == nil {
				ret.Date = d1.Date
			} else {
				ret.Date.New = d1.Date.New
			}
		}
		if d1.Status != nil {
			if ret.Status == nil {
				ret.Status = d1.Status
			} else {
				ret.Status.New = d1.Status.New
			}
		}

		var a models.User
		id, _ := strconv.ParseUint(logs[i].UserId, 10, 64)

		c.Maker.Get(ctx, &a, uint(id), false, nil)

		for _, id := range se {
			j, _ := strconv.ParseUint(id, 10, 64)
			eids[j] = change{Type: models.UNCHANGED}
		}
		ne := strings.Split(next.EntryIDs, ",")
		removed := utils.DiffStringSlices(se, ne)
		added := utils.DiffStringSlices(ne, se)
		for _, id := range removed {
			j, _ := strconv.ParseUint(id, 10, 64)
			eids[j] = change{Type: models.REMOVED, Who: a.Name(), ChangeComment: next.ChangeComment}
		}
		for _, id := range added {
			j, _ := strconv.ParseUint(id, 10, 64)
			if _, ok := eids[j]; ok && eids[j].Type == models.REMOVED {
				eids[j] = change{Type: models.UNCHANGED}
			} else if !ok {
				eids[j] = change{Type: models.ADDED, Who: a.Name(), ChangeComment: next.ChangeComment}
			}
		}

	}
	ret.Entries = c.entryChanges(ctx, &current, eids)

	return ret, nil
}

func (c *Invoice) entryChanges(ctx context.Context, current *models.Invoice, eids map[uint64]change) []*models.TimeEntryDelta {
	db := c.Maker.DB().WithContext(ctx)
	var ret []*models.TimeEntryDelta
	for k, v := range eids {
		var beg, end models.AuditLog
		var delta *models.TimeEntryDelta
		var old, new models.Entry
		switch v.Type {
		case models.UNCHANGED: // item was there since creation
			db.Where("object_id=? and table_name=? and created_at < ?", k, "entries", current.PreviousStatusChange).Order("created_at desc").First(&beg)
			db.Where("object_id=? and table_name=? and created_at > ?", k, "entries", current.PreviousStatusChange).Order("created_at desc").First(&end)
		case models.ADDED: // item was added after creation
			db.Where("object_id=? and table_name=? and created_at < ?", k, "entries", current.CreatedAt).Order("created_at desc").First(&beg)
			db.Where("object_id=? and table_name=? and created_at > ?", k, "entries", current.CreatedAt).Order("created_at desc").First(&end)

		case models.REMOVED: //item was removed after creation
			db.Where("object_id=? and table_name=? and created_at < ?", k, "entries", current.LastStatusChange).Order("created_at desc").First(&beg)
		}
		if end.Id != "" || v.Type != models.UNCHANGED {
			if v.Type == models.ADDED && end.Id == "" {
				json.Unmarshal(beg.Data, &new)
				json.Unmarshal(end.Data, &old)
			} else {
				json.Unmarshal(beg.Data, &old)
				json.Unmarshal(end.Data, &new)
			}
			c.loadEntryAssociations(ctx, &old)
			c.loadEntryAssociations(ctx, &new)

			uid := end.UserId
			if v.Type != models.UNCHANGED {
				uid = v.Who
			} else {
				var a models.User
				id, _ := strconv.ParseUint(uid, 10, 64)
				c.Maker.Get(ctx, &a, uint(id), false, nil)
				uid = a.Name()
			}

			delta = old.Delta(&new, uid, v.ChangeComment)
			if v.Type == models.UNCHANGED && delta.IsEmpty() {
				continue
			}
			delta.InvoiceRelation = v.Type
			delta.ChangeComment = v.ChangeComment
			delta.Who = v.Who
			delta.ID = uint(k)
			ret = append(ret, delta)
		}
	}
	return ret
}

func (c *Invoice) loadEntryAssociations(ctx context.Context, entry *models.Entry) {
	db := c.Maker.DB().WithContext(ctx)
	if entry.ActivityExpenseCodeID != 0 {
		entry.ActivityExpenseCode = &models.TaskCode{}
		db.Model(&entry).Association("ActivityExpenseCode").Find(entry.ActivityExpenseCode)
	}
	if entry.PhaseTaskCodeID != 0 {
		entry.PhaseTaskCode = &models.TaskCode{}
		db.Model(&entry).Association("PhaseTaskCode").Find(entry.PhaseTaskCode)
	}
	entry.User = &models.User{}
	db.Model(&entry).Association("User").Find(entry.User)
}

func (c *Invoice) GetNextState(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	db := c.Maker.DB().WithContext(ctx)
	invoice := models.Invoice{}
	invoice.ID = urlParams.GetID()
	var status models.InvoiceStatus

	db.Preload("Client").Preload("Status").Find(&invoice)
	admin := isAdmin(ctx)
	db.Order("id asc").Where("id = ?", invoice.StatusID+1).Find(&status)
	if !admin {
		if fmt.Sprintf("%d", *invoice.Client.User1ID) != ctx.Value(auth.USERID).(string) || invoice.Status.ApprovalLevel != models.MANAGING_ATTORNEY {
			return nil, nil
		}
	}
	return status, nil
}

package controllers

import (
	"context"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
	"gorm.io/gorm"
)

type Tag struct {
	Base
}

type TagFilters struct {
	models.Paging
	models.Sort
	ClientId string
	Minimal  bool
	Name     string
}

func (tf *TagFilters) PageAndSearch(ctx context.Context, db *gorm.DB) (ps PageAndSearch, preload []any) {
	ps = PageAndSearch{Paging: tf.Paging, Sort: tf.Sort}
	ps.Search = make(models.Search)
	if tf.Name != "" {
		ps.Search["name"] = tf.Name
	}
	return
}

func (c *Tag) Index(ctx context.Context, urlParams Empty, queryParams TagFilters, body Empty) (any, error) {
	var model models.Tags
	return c.List(ctx, &queryParams, &model, false, nil, nil)
}

package controllers

import (
	"context"
	"fmt"
	"strings"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
	perrors "github.com/ontologics/parmenides/errors"
	"gorm.io/gorm"
)

type Payment struct {
	Base
}

type PaymentFilters struct {
	models.Paging
	models.Sort
	Id         string
	ClientName string
	Amount     string
	Reference  string
}

func (pf *PaymentFilters) PageAndSearch(ctx context.Context, db *gorm.DB) (ps PageAndSearch, preload []any) {
	ps = PageAndSearch{Paging: pf.Paging, Sort: pf.Sort}
	if pf.Id != "" {
		ps.Search = make(models.Search)
		ps.Search["payments.id"] = pf.Id
	}

	if pf.ClientName != "" {
		ps.Restrictions = append(ps.Restrictions,
			models.Restriction{Field: `concat(Client.number,ifnull(Client__Parent.name,""), Client.name)`, Operator: "like", Value: "%" + strings.ReplaceAll(pf.ClientName, " - ", "") + "%"})
	}

	if pf.Amount != "" {
		ps.Restrictions = append(ps.Restrictions,
			models.Restriction{Field: `payments.amount`, Operator: "like", Value: pf.Amount + "%"})
	}

	if pf.Reference != "" {
		ps.Restrictions = append(ps.Restrictions,
			models.Restriction{Field: `payments.reference`, Operator: "like", Value: pf.Reference + "%"})
	}

	preload = append(preload,
		models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
			return db.Joins("Client", func(db *gorm.DB) *gorm.DB { return db.Select("Name", "Number") })
		}},
		models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
			return db.Joins("Client.Parent", func(db *gorm.DB) *gorm.DB { return db.Select("Name", "Number") })
		}},
	)
	return
}

type PaymentApplications struct {
	PaymentApplications models.PaymentApplications `json:"paymentApplications"`
}

func (c *Payment) Index(ctx context.Context, urlParams Empty, queryParams PaymentFilters, body Empty) (any, error) {
	var model models.Payments

	return c.List(ctx, &queryParams, &model, false, nil, nil)
}

func (c *Payment) Create(ctx context.Context, urlParams Empty, queryParams Empty, body models.PaymentInput) (any, error) {

	m := &models.Payment{PaymentInput: body}
	if err := c.Maker.Create(ctx, m, nil); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *Payment) Update(ctx context.Context, urlParams ModelID, queryParams Empty, body models.PaymentInput) (any, error) {
	var m models.Payment
	ret, err := c.Change(ctx, urlParams.GetID(), &m, &models.Payment{PaymentInput: body}, nil)
	return ret, err
}

func (c *Payment) Read(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var m models.Payment
	c.Maker.Get(ctx, &m, urlParams.GetID(), true, []string{"Client"})
	if m.ID == 0 {
		return nil, nil
	}
	return m, nil
}

// TODO: refactor into smaller components
func (c *Payment) Apply(ctx context.Context, urlParams ModelID, queryParams Empty, body PaymentApplications) (any, error) {
	//get the payment
	var payment models.Payment
	invoiceIDs := make(map[uint]bool)
	c.Maker.Get(ctx, &payment, urlParams.GetID(), false, nil)

	remaining := payment.Amount - payment.AppliedAmount

	if remaining == 0 {
		return nil, perrors.BadRequestError{Msg: "no funds remaining"}
	}
	tx := c.Maker.DB().WithContext(ctx).Begin()

	for _, pa := range body.PaymentApplications {
		var entry models.Entry
		c.Maker.Get(ctx, &entry, pa.EntryID, false, nil)
		if entry.ID == 0 {
			tx.Rollback()
			return nil, perrors.NotFoundError{Msg: fmt.Sprintf("entry %d not found", entry.ID)}
		}
		var invoice models.Invoice
		if _, ok := invoiceIDs[*entry.InvoiceID]; !ok {
			c.Maker.Get(ctx, &invoice, *entry.InvoiceID, false, []string{"Status"})
			if invoice.Status.IsDraft {
				tx.Rollback()
				return nil, perrors.BadRequestError{Msg: fmt.Sprintf("entry %d not on a finialized invoice", entry.ID)}
			}
			invoiceIDs[*entry.InvoiceID] = true
		}

		if pa.Amount > entry.BalanceRemaining {
			pa.Amount = entry.BalanceRemaining
		}

		if pa.Amount > remaining {
			tx.Rollback()
			return nil, perrors.BadRequestError{Msg: "insufficent funds"}
		}

		remaining -= pa.Amount
		err := c.apply(ctx, &pa, tx)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}
	payment.AppliedAmount = payment.Amount - remaining

	c.Maker.Update(ctx, &payment, map[string]any{"AppliedAmount": payment.AppliedAmount}, tx)

	for k, _ := range invoiceIDs {
		err := c.updateBalanceRemaining(k, tx)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	err := tx.Commit().Error
	return nil, err
}

func (c *Payment) updateBalanceRemaining(invoiceID uint, tx *gorm.DB) error {
	var br float64

	tx.Raw("SELECT sum(total-balance_remaining) as total from entries where invoice_id = ?", invoiceID).Scan(&br)

	sql := `update invoices
	set balance_remaining = invoices.total - ?,
	status_id = case 
	when total - ? = 0 then 6
	else status_id
	end
	where id=?`

	return tx.Exec(sql, br, br, invoiceID).Error
}

func (c *Payment) apply(ctx context.Context, pa *models.PaymentApplication, tx *gorm.DB) error {
	err := c.Maker.Create(ctx, pa, tx)
	if err != nil {
		return err
	}
	sql := `update entries set balance_remaining = balance_remaining - ? where id = ?`
	err = tx.WithContext(ctx).Exec(sql, pa.Amount, pa.EntryID).Error
	return err
}

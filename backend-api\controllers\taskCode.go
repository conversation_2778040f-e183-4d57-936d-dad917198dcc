package controllers

import (
	"context"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
)

type SearchCode struct {
	Code string
	Type string
}

type TaskCode struct {
	Base
}

func (c *TaskCode) Index(ctx context.Context, urlParams Empty, queryParams SearchCode, body Empty) (any, error) {
	var model models.TaskCodes
	ps := PageAndSearch{Paging: models.Paging{Page: 1, Limit: 1000}, Search: map[string]string{"code": queryParams.Code}}
	if queryParams.Type != "" {
		if queryParams.Type == "E" {
			ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "type", Operator: "in", Value: []string{"E", "V"}})
		} else {
			ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "type", Operator: "=", Value: queryParams.Type})
		}
	}
	return c.List(ctx, &ps, &model, false, nil, nil)

}

func (c *TaskCode) Create(ctx context.Context, urlParams Empty, queryParams Empty, body models.TaskCodeInput) (any, error) {
	b := models.TaskCode{TaskCodeInput: body}
	if err := c.Maker.Create(ctx, &b, nil); err != nil {
		return nil, err
	}
	return &b, nil
}

func (c *TaskCode) Update(ctx context.Context, urlParams ModelID, queryParams Empty, body models.TaskCodeInput) (any, error) {
	var m models.TaskCode
	return c.Change(ctx, urlParams.GetID(), &m, &models.TaskCode{TaskCodeInput: body}, nil)
}

func (c *TaskCode) Read(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var billingCode models.TaskCode
	if urlParams.GetID() == 0 {
		return nil, nil
	}
	c.Maker.Get(ctx, &billingCode, uint(urlParams.GetID()), false, nil)
	if billingCode.ID == 0 {
		return nil, nil
	}
	return billingCode, nil
}

func (c *TaskCode) Delete(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) error {
	if urlParams.GetID() == 0 {
		return nil
	}
	m := &models.TaskCode{}
	m.ID = urlParams.GetID()
	err := c.Maker.Delete(ctx, m, m.ID, nil)
	return err
}

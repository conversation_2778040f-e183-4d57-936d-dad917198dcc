package controllers

import (
	"context"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
)

type PermissionLevel struct {
	Base
}

func (c *PermissionLevel) Index(ctx context.Context, urlParams Empty, queryParams PageAndSearch, body Empty) (any, error) {
	var model models.PermissionLevels
	queryParams.Sort.Order = "desc"
	queryParams.Sort.OrderBy = "id"
	return c.List(ctx, queryParams, &model, false, nil, nil)

}

func (c *PermissionLevel) Create(ctx context.Context, urlParams Empty, queryParams Empty, body models.PermissionLevelInput) (any, error) {
	m := &models.PermissionLevel{PermissionLevelInput: body}
	if err := c.Maker.Create(ctx, m, nil); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *PermissionLevel) Update(ctx context.Context, urlParams ModelID, queryParams Empty, body models.PermissionLevelInput) (any, error) {
	var m models.PermissionLevel
	return c.Change(ctx, urlParams.GetID(), &m, &models.PermissionLevel{PermissionLevelInput: body}, nil)
}

func (c *PermissionLevel) Read(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var role models.PermissionLevel
	c.Maker.Get(ctx, &role, uint(urlParams.GetID()), false, nil)
	if role.ID == 0 {
		return nil, nil
	}

	return role, nil
}

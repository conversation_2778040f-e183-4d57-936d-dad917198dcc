package controllers

import (
	"context"
	"slices"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
	"github.com/Matterhorn-Back-Office-Solutions/backend-api/scopes"
	"github.com/ontologics/parmenides/auth"
	"gorm.io/gorm"
)

type OmniSearch struct {
	Base
}

type Query struct {
	Q string `json:"q"`
}

func (qf *Query) restrictToOwn(ctx context.Context, db *gorm.DB) *models.Restriction {
	uc := ctx.Value(auth.CLAIMS).(models.Claims)
	if slices.Contains(uc.Scope, string(scopes.CLIENT_VIEW_OWN)) {
		var ids []string
		db.Raw("select clients.id from matters join clients on (matters.client_id = clients.id) where user1_id = ?", uc.User.ID).Scan(&ids)
		if len(ids) > 0 {
			sr := []models.Restriction{}
			sr = append(sr, models.Restriction{Field: "id", Operator: "in", Value: ids, Joiner: models.AND})
			sr = append(sr, models.Restriction{Field: "type", Operator: "=", Value: "client", Joiner: models.AND})
			return &models.Restriction{Restrictions: &sr, Joiner: models.AND}
		}

		db.Raw("select c.id from rates r join clients c on (c.id = r.client_id or c.parent_id = r.client_id) where user_id = ?", uc.User.ID).Scan(&ids)
		if len(ids) > 0 {
			sr := []models.Restriction{}
			sr = append(sr, models.Restriction{Field: "id", Operator: "in", Value: ids, Joiner: models.AND})
			sr = append(sr, models.Restriction{Field: "type", Operator: "=", Value: "client", Joiner: models.AND})
			return &models.Restriction{Restrictions: &sr, Joiner: models.AND}
		}
	}
	return nil
}
func (qf *Query) PageAndSearch(ctx context.Context, db *gorm.DB) (ps PageAndSearch, preload []any) {
	ps = PageAndSearch{Search: map[string]string{"name": qf.Q}, Sort: models.Sort{OrderBy: "name", Order: "asc"}}

	r := qf.restrictToOwn(ctx, db)
	if r != nil {
		ps.Restrictions = append(ps.Restrictions, *r)
	}
	return
}

func (c *OmniSearch) Index(ctx context.Context, urlParams Empty, queryParams Query, body Empty) (any, error) {
	return c.List(ctx, &queryParams, &models.SearchResults{}, true, nil, nil)
}

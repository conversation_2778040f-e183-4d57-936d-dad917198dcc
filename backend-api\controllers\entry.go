package controllers

import (
	"context"
	"slices"
	"strings"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
	"github.com/Matterhorn-Back-Office-Solutions/backend-api/scopes"
	"github.com/ontologics/parmenides/auth"
	"gorm.io/gorm"
)

type Entry struct {
	Base
}

type EntryFilters struct {
	models.Paging
	models.Sort
	MatterId           string
	Type               string
	InvoiceId          string
	IsReady            bool
	OnHold             bool
	IsBillable         bool
	MatterNumber       string
	User               string
	ClientId           string
	LoadClientCurrency bool
	ClientName         string
	Date               string
	StartDate          string
	EndDate            string
	Minimal            bool
	StatusId           string
}

func (ef *EntryFilters) restrictToOwn(ctx context.Context, db *gorm.DB) *models.Restriction {
	uc := ctx.Value(auth.CLAIMS).(models.Claims)
	tx := db.WithContext(ctx)
	if slices.Contains(uc.Scope, string(scopes.ENTRY_OWN)) {
		res := []models.Restriction{}
		if ef.InvoiceId != "" {
			var ids []string
			tx.Raw("select matters.id from matters join clients on (matters.client_id = clients.id) where clients.user1_id = ?", uc.User.ID).Scan(&ids)
			if len(ids) > 0 {
				res = append(res, models.Restriction{Field: "Matter.id", Value: ids, Operator: "in", Joiner: models.OR})
			}
			tx.Raw("select m.id from rates r join matters m on (r.client_id = m.client_id) where user_id = ?", uc.User.ID).Scan(&ids)
			if len(ids) > 0 {
				res = append(res, models.Restriction{Field: "Matter.id", Value: ids, Operator: "in", Joiner: models.OR})
			}
		}
		var uids []string
		tx.Raw("select matters.id from matters join clients on (matters.client_id = clients.id) where clients.user1_id = ?", uc.User.ID).Scan(&uids)
		if len(uids) > 0 {
			res = append(res, models.Restriction{Field: "Matter.id", Value: uids, Operator: "in", Joiner: models.OR})
		}
		res = append(res, models.Restriction{Field: "user_id", Value: uc.User.ID, Operator: "=", Joiner: models.OR})
		return &models.Restriction{Joiner: models.AND, Restrictions: &res}
	}
	return nil
}

func (ef *EntryFilters) PageAndSearch(ctx context.Context, db *gorm.DB) (ps PageAndSearch, preload []any) {
	ps = PageAndSearch{Paging: ef.Paging, Sort: ef.Sort}
	r := ef.restrictToOwn(ctx, db)
	if r != nil {
		ps.Restrictions = append(ps.Restrictions, *r)
	}
	if ef.ClientName != "" || ef.OrderBy == "Client.number" {
		if ps.Sort.OrderBy == "" {
			ps.Sort.OrderBy = "Matter__Client.number" // Why did I do this?
		}
		ps.Restrictions = append(ps.Restrictions,
			models.Restriction{Field: `concat(Matter__Client.number,ifnull(Matter__Client__Parent.name,""), Matter__Client.name)`, Operator: "like", Value: "%" + strings.ReplaceAll(ef.ClientName, " - ", "") + "%"})
	}

	if ef.MatterNumber != "" || ef.OrderBy == "Matter.number" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "Matter.number", Value: "%" + ef.MatterNumber + "%", Operator: "like", Joiner: models.AND})
	}
	if ef.ClientId != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "Matter.client_id", Value: ef.ClientId, Operator: "=", Joiner: models.AND})
	}
	if ef.User != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "user_id", Value: ef.User, Operator: "=", Joiner: models.AND})
	}
	if ef.MatterId != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "entries.matter_id", Value: ef.MatterId, Operator: "=", Joiner: models.AND})
	}
	if ef.InvoiceId != "" {
		res := models.Restriction{Joiner: models.AND}
		res.Restrictions = &[]models.Restriction{{Field: "invoice_id", Operator: "=", Value: ef.InvoiceId}, {Field: "invoice_id", Operator: "is", Value: nil, Joiner: models.OR}}

		ps.Restrictions = append(ps.Restrictions, res)
	}
	if ef.Type != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "entries.type", Value: ef.Type, Operator: "=", Joiner: models.AND})
	}
	if ef.IsReady || ef.OnHold {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "is_ready", Value: ef.IsReady, Operator: "=", Joiner: models.AND})
	}
	if ef.IsBillable {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "is_billable", Value: ef.IsBillable, Operator: "=", Joiner: models.AND})
	}
	if ef.Date != "" {
		ps.Search = make(models.Search)
		ps.Search["date"] = ef.Date
	}
	if ef.StartDate != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "entries.date", Value: ef.StartDate, Operator: ">=", Joiner: models.AND})
	}
	if ef.EndDate != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "entries.date", Value: ef.EndDate, Operator: "<=", Joiner: models.AND})
	}

	if ef.StatusId == "0" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "Invoice.status_id", Value: nil, Operator: "is", Joiner: models.AND})
	} else if ef.StatusId != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "Invoice.status_id", Value: ef.StatusId, Operator: "=", Joiner: models.AND})
	}
	return
}

func (c *Entry) Index(ctx context.Context, urlParams Empty, queryParams EntryFilters, body Empty) (any, error) {
	var model models.Entries
	var preload []any

	if queryParams.Minimal {
		preload = []any{
			models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
				return db.Joins("Invoice", db.Select("Invoice.id", "Invoice.status_id"))
			}},
			models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
				return db.Joins("Invoice.Status", db.Select("Invoice__Status.name"))
			}},
			models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
				return db.Joins("Matter", db.Select("Matter.number", "Matter.client_id"))

			}},
			models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
				return db.Joins("Matter.Client", db.Select("Matter__Client.id", "Matter__Client.number", "Matter__Client.name", "Matter_Client.parent_id"))

			}},
			models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
				return db.Joins("User", db.Select("User.id", "User.first_name", "User.last_name"))

			}},
			models.CustomPreload{CustomFunc: func(db *gorm.DB) *gorm.DB {
				return db.Joins("Matter.Client.Parent", db.Select("Matter__Client__Parent.number", "Matter__Client__Parent.name"))

			}},
		}
	} else {
		preload = []any{"Matter", "Matter.Client", "Matter.Client.Parent", "PhaseTaskCode", "ActivityExpenseCode", "User", "Currency", "Invoice"}
	}
	if queryParams.LoadClientCurrency {
		preload = append(preload, "Matter.Client.Currency")
	}

	ret, err := c.List(ctx, &queryParams, &model, false, preload, nil)
	if queryParams.LoadClientCurrency {
		for i := 0; i < len(model); i++ {
			model[i].RateClientCurrency = model[i].Currency.Convert(model[i].Matter.Client.Currency, model[i].Rate)
			model[i].TotalClientCurrency = model[i].Currency.Convert(model[i].Matter.Client.Currency, model[i].Total)
		}

	}
	return ret, ConvertToParmenidesError(err)
}

func (c *Entry) Create(ctx context.Context, urlParams Empty, queryParams Empty, body models.EntryInput) (any, error) {
	uid := c.getUID(ctx)
	m := &models.Entry{EntryInput: body, CreatedByID: uint(uid)}
	if err := c.Maker.Create(ctx, m, nil); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *Entry) Update(ctx context.Context, urlParams ModelID, queryParams Empty, body models.EntryInput) (any, error) {
	var m models.Entry
	ret, err := c.Change(ctx, urlParams.GetID(), &m, &models.Entry{EntryInput: body}, nil)
	return ret, ConvertToParmenidesError(err)
}

func (c *Entry) Read(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var timeEntry models.Entry
	return c.Get(ctx, &timeEntry, uint(urlParams.GetID()), true, []string{"Matter", "Matter.Rate", "Matter.Client", "Matter.Client.Parent", "Invoice.Status"})
}

func (c *Entry) Name(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	return "", nil
}

type FieldValue struct {
	Field string `json:"field"`
	Value any    `json:"value"`
}
type Batch struct {
	BatchInfo []FieldValue `json:"batchInfo" valid:"in(phaseTask|timekeeperId,activityExpense,isBillible)"`
	EntryIDs  []uint       `json:"entryIds"`
}

func (c *Entry) BatchUpdate(ctx context.Context, urlParams Empty, queryParams Empty, body Batch) (any, error) {
	updates := map[string]any{}
	for _, fv := range body.BatchInfo {
		updates[models.ToSnakeCase(fv.Field)] = fv.Value
	}

	return nil, ConvertToParmenidesError(c.Maker.DB().WithContext(ctx).Model(&models.Entry{}).Where("id in ?", body.EntryIDs).UpdateColumns(updates).Error)
}

func (c *Entry) Delete(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var m models.Entry
	m.ID = urlParams.GetID()
	c.Maker.DB().WithContext(ctx).Delete(&m)
	return nil, nil
}

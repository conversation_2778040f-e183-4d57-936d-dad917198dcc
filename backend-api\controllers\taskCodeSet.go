package controllers

import (
	"context"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
	"github.com/ontologics/parmenides"
)

type TaskCodeSetFilters struct {
	models.Paging
	SetId      string
	TaskCodeId string
	Recursive  bool
}

func (tf *TaskCodeSetFilters) PageAndSearch(context.Context) (ps PageAndSearch, preload []any) {
	ps = PageAndSearch{Paging: tf.Paging}
	ps.Search = make(models.Search)
	if tf.TaskCodeId != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "task_code_id", Operator: "=", Value: tf.TaskCodeId})
	}
	if tf.Recursive {
		var r []models.Restriction
		r = append(r, models.Restriction{Field: "Client.id", Operator: "=", Value: tf.SetId}, models.Restriction{Field: "Client.parent_id", Operator: "=", Value: tf.SetId, Joiner: models.OR})
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Restrictions: &r, Joiner: models.AND})
	} else if tf.SetId != "" {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "task_code_sets.task_code_set_id", Operator: "=", Value: tf.SetId})
	}
	preload = append(preload, "TaskCode")
	return
}

type TaskCodeSet struct {
	Base
}

func (c *TaskCodeSet) Index(ctx context.Context, urlParams Empty, queryParams TaskCodeSetFilters, body Empty) (any, error) {
	var model models.TaskCodeSets
	return c.List(ctx, &queryParams, &model, false, []any{"Client", "TaskCode"}, nil)
}

func (c *TaskCodeSet) Create(ctx context.Context, urlParams Empty, queryParams Empty, body models.TaskCodeSetInput) (any, error) {
	b := models.TaskCodeSet{TaskCodeSetInput: body}
	if err := c.Maker.Create(ctx, &b, nil); err != nil {
		return nil, err
	}
	return &b, nil
}

func (c *TaskCodeSet) Update(ctx context.Context, urlParams ModelID, queryParams Empty, body models.TaskCodeSetInput) (any, error) {
	var m models.TaskCodeSet
	return c.Change(ctx, urlParams.GetID(), &m, &models.TaskCodeSet{TaskCodeSetInput: body}, nil)
}

func (c *TaskCodeSet) Read(ctx context.Context, urlParams ModelID, queryParams TaskCodeSetFilters, body Empty) (any, error) {
	var model models.TaskCodeSets
	ps := PageAndSearch{Paging: queryParams.Paging}
	ps.Search = make(models.Search)
	ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "task_code_id", Operator: "=", Value: urlParams.GetID()})

	return c.List(ctx, ps, &model, true, nil, nil)
}
func (c *TaskCodeSet) Export(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var bcs models.TaskCodeSet
	if urlParams.GetID() == 0 {
		return nil, nil
	}
	c.Maker.Get(ctx, &bcs, uint(urlParams.GetID()), true, nil)
	ret := "code,isPhaseTask,identifier"

	cr := parmenides.ResultWithCustomHeader{Body: ret, Header: map[string]string{"Content-Type": "application/csv", "Content-Disposition": `attachment; filename="billingCodeSet.csv"`}}

	return cr, nil
}

func (c *TaskCodeSet) Delete(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	if urlParams.GetID() == 0 {
		return nil, nil
	}
	err := c.Maker.DB().WithContext(ctx).WithContext(ctx).Delete(&models.TaskCodeSet{}, "id=?", urlParams.GetID()).Error
	return nil, err
}

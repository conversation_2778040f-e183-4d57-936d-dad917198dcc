{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Package",
      "type": "go",
      "request": "launch",
      "outputMode": "remote",
      "program": "./main.go",
      "env": {
        "ENV": "dev",
        "AWS_REGION": "us-east-1",
        "AUTH": "false",
        "API_CONF": "./testconfig.json"
      },
      "buildFlags": "-mod vendor"
    },
    {
      "name": "Launch Package (QA)",
      "type": "go",
      "request": "launch",
      //            "mode": "auto",
      "program": "./main.go",
      "env": {
        "ENV": "qa",
        "AWS_REGION": "us-east-1",
        "AUTH": "false",
        "API_CONF": "./testconfig.json"
      },
      "buildFlags": "-mod vendor"
    }
  ]
}

package controllers

import (
	"context"
	"strconv"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
)

type Rate struct {
	Base
}

type RateFilter struct {
	ClientId int
	models.Paging
	models.Sort
}

type UserRateParams struct {
	UserId string
	ID     string
}

func (u UserRateParams) UserID() uint {
	ret, _ := strconv.Atoi(u.UserId)
	return uint(ret)
}

func (u UserRateParams) ClientID() uint {
	ret, _ := strconv.Atoi(u.ID)
	return uint(ret)
}

func (c *Rate) Index(ctx context.Context, urlParams Empty, queryParams RateFilter, body Empty) (any, error) {
	var model models.Rates
	ps := PageAndSearch{Paging: queryParams.Paging, Sort: queryParams.Sort}
	if queryParams.ClientId != 0 {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "client_id", Operator: "=", Value: queryParams.ClientId})

	}
	return c.List(ctx, ps, &model, true, nil, nil)
}

func (c *Rate) Create(ctx context.Context, urlParams Empty, queryParams Empty, body models.RateInput) (any, error) {
	m := &models.Rate{RateInput: body}
	if err := c.Maker.Create(ctx, m, nil); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *Rate) Update(ctx context.Context, urlParams ModelID, queryParams Empty, body models.RateInput) (any, error) {
	var m models.Rate
	return c.Change(ctx, urlParams.GetID(), &m, &models.Rate{RateInput: body}, nil)
}

func (c *Rate) Read(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var rate models.Rate
	c.Maker.Get(ctx, &rate, uint(urlParams.GetID()), true, []string{"RateTable.Currency", "RateTable.User"})
	if rate.ID == 0 {
		return nil, nil
	}

	return rate, nil
}

func (c *Rate) Get(ctx context.Context, urlParams UserRateParams, queryParams Empty, body Empty) (any, error) {
	// look for rate for user at matter level
	var ret models.Rate
	err := c.Maker.DB().WithContext(ctx).Where("client_id=? AND (user_id=? or user_id = 0 or user_id is null)", urlParams.ClientID(), urlParams.UserID()).Order("client_id desc").First(&ret).Error
	return ret, toParmenidesError(err)
}

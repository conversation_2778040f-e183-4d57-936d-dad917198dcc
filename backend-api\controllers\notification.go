package controllers

import (
	"bytes"
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"os"
	"text/template"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
	"github.com/ontologics/parmenides"

	"gopkg.in/gomail.v2"
)

type SMTP struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Email    string `json:"email"`
	Password string `json:"password"`
}
type NotificationConfig struct {
	SMTP SMTP `json:"smtp" asm:"smtp"`
}

var notificationControllerInstance *Notification

type Notification struct {
	Base
	CustomConfig *NotificationConfig
}

func GetNotificationController() *Notification {
	if notificationControllerInstance == nil {
		notificationControllerInstance = &Notification{}
	}
	return notificationControllerInstance
}

func (c *Notification) Create(ctx context.Context, urlParams Empty, queryParams Empty, body models.NotificationInput) (any, error) {
	n := models.Notification{NotificationInput: body}

	var a models.User

	c.Maker.Get(ctx, &a, n.UserID, false, nil)

	if a.ID == 0 {
		return nil, errors.New("user not found")
	}

	// if a.NotificationType == models.NOTIFICATION_BOTH || a.NotificationType == models.NOTIFICATION_PORTAL {
	// 	c.Maker.Create(ctx, &n)
	// }
	// if a.NotificationType == models.NOTIFICATION_BOTH || a.NotificationType == models.NOTIFICATION_PORTAL {
	//send out email
	c.email(*a.Email, n)
	// }

	return nil, nil
}

func (b *Notification) email(address string, data models.Notification) error {
	var buf bytes.Buffer

	t := template.Must(template.ParseFiles("./templates/notification.html"))
	err := t.Execute(&buf, data)
	if err != nil {
		return fmt.Errorf("failed to create email from template %s", err)
	}

	m := gomail.NewMessage()

	// Set E-Mail sender
	m.SetHeader("From", "Matterhorn<<EMAIL>>")

	// Set E-Mail receivers
	m.SetHeader("To", address)

	// Set E-Mail subject
	m.SetHeader("Subject", data.Title)

	// Set E-Mail body. You can set plain text or html with text/html
	m.SetBody("text/html", buf.String())
	var d *gomail.Dialer

	if os.Getenv("ENV") == "prod" {
		d = gomail.NewDialer(b.CustomConfig.SMTP.Host, b.CustomConfig.SMTP.Port, b.CustomConfig.SMTP.Email, b.CustomConfig.SMTP.Password)
	} else {
		d = &gomail.Dialer{Host: b.CustomConfig.SMTP.Host, Port: b.CustomConfig.SMTP.Port}
		d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
		parmenides.Logger.Debugf("+v\n", b.CustomConfig.SMTP)
	}

	// Now send E-Mail
	return d.DialAndSend(m)
}

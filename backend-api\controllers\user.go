package controllers

import (
	"context"
	"fmt"
	"net/http"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
	"github.com/google/uuid"
	"github.com/ontologics/parmenides/errors"
	"gorm.io/gorm"
)

type UserFilters struct {
	Name      string
	FirstName string
	LastName  string
	Initials  string
	Email     string
	models.Paging
	models.Sort
	IsActive         bool
	HasRateForClient string
	IsTimekeeper     bool
	IsAdmin          bool
	Minimal          bool
}

func (uf *UserFilters) PageAndSearch(ctx context.Context, db *gorm.DB) (ps PageAndSearch, preload []any) {
	uf.Sort.OrderBy = models.GetFieldName(uf.Sort.OrderBy)

	if uf.Sort.OrderBy == "Role.name" {
		preload = append(preload, "Role")
	}

	ps = PageAndSearch{Paging: uf.Paging, Sort: uf.Sort}
	ps.Search = make(models.Search)
	if uf.IsActive {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "is_active", Operator: "=", Value: 1})
	}
	if uf.IsAdmin {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "permission_level_id", Operator: "=", Value: 1})
	}
	if uf.Name != "" {
		nr := []models.Restriction{}
		nr = append(nr, models.Restriction{Field: "last_name", Operator: models.CONDITION_LIKE, Value: uf.Name})
		nr = append(nr, models.Restriction{Field: "first_name", Operator: models.CONDITION_LIKE, Value: uf.Name, Joiner: models.OR})

		ps.Restrictions = append(ps.Restrictions, models.Restriction{Restrictions: &nr})
	}
	if uf.LastName != "" {
		ps.Search["last_name"] = uf.LastName
	}
	if uf.FirstName != "" {
		ps.Search["first_name"] = uf.FirstName
	}
	if uf.Initials != "" {
		ps.Search["initials"] = uf.Initials
	}
	if uf.Email != "" {
		ps.Search["email"] = uf.Email
	}

	if uf.HasRateForClient != "" {
		nr := []models.Restriction{}
		nr2 := []models.Restriction{}

		nr = append(nr, models.Restriction{Field: "rates.client_id", Operator: "=", Value: uf.HasRateForClient, Joiner: models.AND})
		nr2 = append(nr2, models.Restriction{Field: "clients.id", Operator: "=", Value: uf.HasRateForClient})
		nr2 = append(nr2, models.Restriction{Field: "rates.client_id", Operator: "is", Value: nil, Joiner: models.AND})
		nr = append(nr, models.Restriction{Restrictions: &nr2, Joiner: models.OR})
		preload = append(preload, "join rates on (users.id=rates.user_id or rates.user_id = 0) join clients on (rates.client_id = clients.id or rates.client_id = clients.parent_id)")

		ps.Restrictions = append(ps.Restrictions, models.Restriction{Restrictions: &nr, Joiner: models.AND})
	}
	if uf.IsTimekeeper {
		ps.Restrictions = append(ps.Restrictions, models.Restriction{Field: "timekeeper_number", Operator: models.CONDITION_GREATER_THAN, Value: 0, Joiner: models.AND})
	}
	return
}

type PasswordResetRequest struct {
	UUID     string `json:"uuid"`
	Password string `json:"password"`
}

type User struct {
	Base
}

func (c *User) Index(ctx context.Context, urlParams Empty, queryParams UserFilters, body Empty) (any, error) {
	var model models.Users
	var fields *[]string
	if queryParams.Minimal {
		fields = &[]string{"users.ID", "FirstName", "LastName"}
	}
	ret, err := c.List(ctx, &queryParams, &model, !queryParams.Minimal, nil, fields)
	return ret, err
}

func (c *User) Create(ctx context.Context, urlParams Empty, queryParams Empty, body models.UserInput) (any, error) {
	req := ctx.Value("Header").(http.Header)
	baseURL := GetBaseURL(req.Get("Referer"))

	body.IsActive = Bool(true)
	m := &models.User{UserInput: body}
	//TODO: Deal with roles correctly once they are defined
	m.JobTitleID = models.String("PT")
	m.PasswordResetID = models.String(uuid.New().String())
	if err := c.Maker.Create(ctx, m, nil); err != nil {
		return nil, ConvertToParmenidesError(err)
	}
	m.Password = nil
	var note models.NotificationInput

	//TODO: add link to notification for password setting
	note.UserID = m.ID
	note.Title = "Welcome to Matterhorn"
	note.Body = fmt.Sprintf(`Your account has been created.<br/> <a href="%s/passwordreset/%s">Click here to get started!</a>`, baseURL, *m.PasswordResetID)
	GetNotificationController().Create(ctx, Empty{}, Empty{}, note)
	return m, nil
}

func (c *User) Update(ctx context.Context, urlParams ModelID, queryParams Empty, body models.UserInput) (any, error) {
	var m models.User

	if body.JobTitle != nil {
		body.JobTitleID = &body.JobTitle.ID
	}

	ret, err := c.Change(ctx, urlParams.GetID(), &m, &models.User{UserInput: body}, nil)
	return ret, err
}

func (c *User) Read(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var user models.User
	return c.Get(ctx, &user, uint(urlParams.GetID()), false, nil)
}

func (c *User) PasswordResetRequest(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {

	req := ctx.Value("Header").(http.Header)
	baseURL := GetBaseURL(req.Get("Referer"))
	var m models.User
	var note models.NotificationInput
	_, err := c.Change(ctx, urlParams.GetID(), &m, map[string]any{"password_reset_id": models.String(uuid.New().String())}, nil)

	if err != nil {
		return nil, err
	}

	note.UserID = m.ID
	note.Title = "Password Reset Request"
	note.Body = fmt.Sprintf(`A password reset request has been submitted for your account.<br/><a href="%s/passwordreset/%s">Click here to change your password.</a>`, baseURL, *m.PasswordResetID)
	GetNotificationController().Create(ctx, Empty{}, Empty{}, note)
	return nil, nil
}

func (c *User) PasswordReset(ctx context.Context, urlParams Empty, queryParams Empty, body PasswordResetRequest) (any, error) {
	var user models.User
	err := c.Maker.DB().WithContext(ctx).Model(&user).Where("password_reset_id=?", body.UUID).First(&user).Error
	if err != nil || user.ID == 0 {
		return nil, errors.NotFoundError{Msg: "invalid reset id"}
	}
	user.Password = &body.Password
	ret, err := c.Change(ctx, user.ID, &user, &user, nil)
	return ret, err
}

func (c *User) NextTimeKeeperID(ctx context.Context, urlParams Empty, querParams Empty, body Empty) (any, error) {
	var ret int
	return ret, ConvertToParmenidesError(c.Maker.DB().WithContext(ctx).Raw("select max(timekeeper_number)+1 from users").Scan(&ret).Error)
}

package controllers

import (
	"context"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
)

type Settings struct {
	Base
}

type SettingsPayload struct {
	Settings models.Settings `json:"settings"`
}

func (c *Settings) Index(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var model models.Settings

	ret, err := c.List(ctx, &queryParams, &model, false, nil, nil)
	return ret, err
}

func (c *Settings) Create(ctx context.Context, urlParams Empty, queryParams Empty, body models.SettingInput) (any, error) {
	m := &models.Setting{SettingInput: body}
	if err := c.Maker.Create(ctx, m, nil); err != nil {
		return nil, ConvertToParmenidesError(err)
	}
	return m, nil
}

func (c *Settings) Update(ctx context.Context, urlParams Empty, queryParams Empty, body models.SettingInput) (any, error) {
	m := &models.Setting{SettingInput: body}
	return m, c.Maker.DB().Save(m).Error
}

func (c *Settings) BulkUpsert(ctx context.Context, urlParams Empty, queryParams Empty, body SettingsPayload) (any, error) {
	tx := c.Maker.DB().WithContext(ctx).Begin()
	for _, setting := range body.Settings {
		var old models.Setting
		tx.Model(&old).First(&old, "name=?", setting.Name)
		setting.ID = old.ID
		err := tx.Save(&setting).Error
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	return body, tx.Commit().Error
}

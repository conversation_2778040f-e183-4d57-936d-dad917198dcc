package controllers

import (
	"context"

	"github.com/Matterhorn-Back-Office-Solutions/backend-api/models"
)

type SubmissionType struct {
	Base
}
type SearchSubmissionType struct {
	Name string
}

func (c *SubmissionType) Index(ctx context.Context, urlParams Empty, queryParams SearchSubmissionType, body Empty) (any, error) {
	var model models.SubmissionTypes
	ps := PageAndSearch{Paging: models.Paging{Page: 1, Limit: 10}, Search: map[string]string{"name": queryParams.Name}}
	return c.List(ctx, ps, &model, false, nil, nil)

}

func (c *SubmissionType) Create(ctx context.Context, urlParams Empty, queryParams Empty, body models.SubmissionType) (any, error) {
	if err := c.Maker.Create(ctx, &body, nil); err != nil {
		return nil, err
	}
	return &body, nil
}

func (c *SubmissionType) Update(ctx context.Context, urlParams ModelID, queryParams Empty, body models.SubmissionType) (any, error) {
	return c.Change(ctx, urlParams.GetID(), &body, body, nil)
}

func (c *SubmissionType) Read(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) (any, error) {
	var billingCode models.SubmissionType
	if urlParams.GetID() == 0 {
		return nil, nil
	}
	c.Maker.Get(ctx, &billingCode, uint(urlParams.GetID()), false, nil)
	if billingCode.ID == 0 {
		return nil, nil
	}
	return billingCode, nil
}

func (c *SubmissionType) Delete(ctx context.Context, urlParams ModelID, queryParams Empty, body Empty) error {
	if urlParams.GetID() == 0 {
		return nil
	}
	m := &models.SubmissionType{}
	m.ID = urlParams.GetID()
	err := c.Maker.Delete(ctx, m, m.ID, nil)
	return err
}
